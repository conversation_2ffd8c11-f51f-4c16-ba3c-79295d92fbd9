using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Services
{
    /// <summary>
    /// Service for analyzing IIS log entries and generating API-wise results
    /// </summary>
    public class ApiAnalysisService
    {
        /// <summary>
        /// Event fired when analysis progress updates
        /// </summary>
        public event EventHandler<ProgressEventArgs>? ProgressUpdated;
        
        /// <summary>
        /// Analyze log entries and generate API-wise results
        /// </summary>
        /// <param name="logEntries">Collection of IIS log entries</param>
        /// <returns>Overall analysis summary with API-wise results</returns>
        public async Task<OverallAnalysisSummary> AnalyzeLogsAsync(List<IISLogEntry> logEntries)
        {
            return await Task.Run(() =>
            {
                var summary = new OverallAnalysisSummary
                {
                    TotalLogEntries = logEntries.Count,
                    AnalysisStartTime = DateTime.Now
                };
                
                if (logEntries.Count == 0)
                {
                    summary.AnalysisEndTime = DateTime.Now;
                    return summary;
                }
                
                // Group entries by API endpoint and HTTP method
                var groupedEntries = logEntries
                    .GroupBy(e => new { e.ApiEndpoint, e.Method })
                    .ToList();
                
                summary.TotalApiEndpoints = groupedEntries.Count;
                
                var processedGroups = 0;
                foreach (var group in groupedEntries)
                {
                    processedGroups++;
                    var progress = (double)processedGroups / groupedEntries.Count * 100;
                    ProgressUpdated?.Invoke(this, new ProgressEventArgs(
                        progress, 
                        $"Analyzing API {processedGroups} of {groupedEntries.Count}: {group.Key.Method} {group.Key.ApiEndpoint}"));
                    
                    var apiResult = AnalyzeApiEndpoint(group.Key.ApiEndpoint, group.Key.Method, group.ToList());
                    summary.ApiResults.Add(apiResult);
                    
                    // Update method distribution
                    if (!summary.MethodDistribution.ContainsKey(group.Key.Method))
                        summary.MethodDistribution[group.Key.Method] = 0;
                    summary.MethodDistribution[group.Key.Method] += group.Count();
                    
                    // Update overall status code distribution
                    foreach (var entry in group)
                    {
                        if (!summary.OverallStatusCodeDistribution.ContainsKey(entry.StatusCode))
                            summary.OverallStatusCodeDistribution[entry.StatusCode] = 0;
                        summary.OverallStatusCodeDistribution[entry.StatusCode]++;
                    }
                }
                
                // Calculate log time span
                if (logEntries.Count > 0)
                {
                    var firstEntry = logEntries.Min(e => e.DateTime);
                    var lastEntry = logEntries.Max(e => e.DateTime);
                    summary.LogTimeSpan = lastEntry - firstEntry;
                }
                
                summary.AnalysisEndTime = DateTime.Now;
                ProgressUpdated?.Invoke(this, new ProgressEventArgs(100, "Analysis completed"));
                
                return summary;
            });
        }
        
        /// <summary>
        /// Analyze a specific API endpoint
        /// </summary>
        private ApiAnalysisResult AnalyzeApiEndpoint(string apiEndpoint, string method, List<IISLogEntry> entries)
        {
            var result = new ApiAnalysisResult
            {
                ApiEndpoint = apiEndpoint,
                Method = method,
                TotalRequests = entries.Count,
                SuccessfulRequests = entries.Count(e => e.IsSuccessful),
                ClientErrors = entries.Count(e => e.IsClientError),
                ServerErrors = entries.Count(e => e.IsServerError),
                AverageResponseTime = entries.Average(e => e.TimeTaken),
                MinResponseTime = entries.Min(e => e.TimeTaken),
                MaxResponseTime = entries.Max(e => e.TimeTaken),
                TotalBytesSent = entries.Sum(e => e.BytesSent),
                TotalBytesReceived = entries.Sum(e => e.BytesReceived),
                FirstRequest = entries.Min(e => e.DateTime),
                LastRequest = entries.Max(e => e.DateTime)
            };
            
            // Calculate status code distribution
            foreach (var entry in entries)
            {
                if (!result.StatusCodeDistribution.ContainsKey(entry.StatusCode))
                    result.StatusCodeDistribution[entry.StatusCode] = 0;
                result.StatusCodeDistribution[entry.StatusCode]++;
                
                // Track unique client IPs
                if (!string.IsNullOrEmpty(entry.ClientIP) && !result.UniqueClientIPs.Contains(entry.ClientIP))
                    result.UniqueClientIPs.Add(entry.ClientIP);
            }
            
            return result;
        }
        
        /// <summary>
        /// Filter analysis results based on criteria
        /// </summary>
        public OverallAnalysisSummary FilterResults(OverallAnalysisSummary summary, Func<ApiAnalysisResult, bool> predicate)
        {
            var filteredSummary = new OverallAnalysisSummary
            {
                TotalLogEntries = summary.TotalLogEntries,
                AnalysisStartTime = summary.AnalysisStartTime,
                AnalysisEndTime = summary.AnalysisEndTime,
                LogTimeSpan = summary.LogTimeSpan,
                MethodDistribution = summary.MethodDistribution,
                OverallStatusCodeDistribution = summary.OverallStatusCodeDistribution
            };
            
            filteredSummary.ApiResults = summary.ApiResults.Where(predicate).ToList();
            filteredSummary.TotalApiEndpoints = filteredSummary.ApiResults.Count;
            
            return filteredSummary;
        }
    }
}
