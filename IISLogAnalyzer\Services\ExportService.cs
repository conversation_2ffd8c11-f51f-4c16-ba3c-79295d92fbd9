using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using CsvHelper;
using IISLogAnalyzer.Models;
using Newtonsoft.Json;

namespace IISLogAnalyzer.Services
{
    /// <summary>
    /// Service for exporting analysis results to various formats
    /// </summary>
    public class ExportService
    {
        /// <summary>
        /// Export analysis results to CSV format
        /// </summary>
        public async Task ExportToCsvAsync(OverallAnalysisSummary summary, string filePath)
        {
            using var writer = new StringWriter();
            using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);
            
            // Write headers
            csv.WriteField("API Endpoint");
            csv.WriteField("Method");
            csv.WriteField("Total Requests");
            csv.WriteField("Successful Requests");
            csv.WriteField("Client Errors");
            csv.WriteField("Server Errors");
            csv.WriteField("Success Rate (%)");
            csv.WriteField("Error Rate (%)");
            csv.WriteField("Average Response Time (ms)");
            csv.WriteField("Min Response Time (ms)");
            csv.WriteField("Max Response Time (ms)");
            csv.WriteField("Total Bytes Sent");
            csv.WriteField("Total Bytes Received");
            csv.WriteField("Average Bytes Sent");
            csv.WriteField("Average Bytes Received");
            csv.WriteField("Unique Clients");
            csv.WriteField("Requests Per Hour");
            csv.WriteField("First Request");
            csv.WriteField("Last Request");
            csv.NextRecord();
            
            // Write data
            foreach (var result in summary.ApiResults.OrderByDescending(r => r.TotalRequests))
            {
                csv.WriteField(result.ApiEndpoint);
                csv.WriteField(result.Method);
                csv.WriteField(result.TotalRequests);
                csv.WriteField(result.SuccessfulRequests);
                csv.WriteField(result.ClientErrors);
                csv.WriteField(result.ServerErrors);
                csv.WriteField(Math.Round(result.SuccessRate, 2));
                csv.WriteField(Math.Round(result.ErrorRate, 2));
                csv.WriteField(Math.Round(result.AverageResponseTime, 2));
                csv.WriteField(result.MinResponseTime);
                csv.WriteField(result.MaxResponseTime);
                csv.WriteField(result.TotalBytesSent);
                csv.WriteField(result.TotalBytesReceived);
                csv.WriteField(Math.Round(result.AverageBytesSent, 2));
                csv.WriteField(Math.Round(result.AverageBytesReceived, 2));
                csv.WriteField(result.UniqueClientCount);
                csv.WriteField(Math.Round(result.RequestsPerHour, 2));
                csv.WriteField(result.FirstRequest.ToString("yyyy-MM-dd HH:mm:ss"));
                csv.WriteField(result.LastRequest.ToString("yyyy-MM-dd HH:mm:ss"));
                csv.NextRecord();
            }
            
            await File.WriteAllTextAsync(filePath, writer.ToString());
        }
        
        /// <summary>
        /// Export analysis results to JSON format
        /// </summary>
        public async Task ExportToJsonAsync(OverallAnalysisSummary summary, string filePath)
        {
            var exportData = new
            {
                Summary = new
                {
                    summary.TotalLogEntries,
                    summary.TotalApiEndpoints,
                    summary.AnalysisStartTime,
                    summary.AnalysisEndTime,
                    LogTimeSpanHours = summary.LogTimeSpan.TotalHours,
                    summary.MethodDistribution,
                    summary.OverallStatusCodeDistribution
                },
                ApiResults = summary.ApiResults.Select(r => new
                {
                    r.ApiEndpoint,
                    r.Method,
                    r.TotalRequests,
                    r.SuccessfulRequests,
                    r.ClientErrors,
                    r.ServerErrors,
                    SuccessRate = Math.Round(r.SuccessRate, 2),
                    ErrorRate = Math.Round(r.ErrorRate, 2),
                    AverageResponseTime = Math.Round(r.AverageResponseTime, 2),
                    r.MinResponseTime,
                    r.MaxResponseTime,
                    r.TotalBytesSent,
                    r.TotalBytesReceived,
                    AverageBytesSent = Math.Round(r.AverageBytesSent, 2),
                    AverageBytesReceived = Math.Round(r.AverageBytesReceived, 2),
                    UniqueClientCount = r.UniqueClientCount,
                    RequestsPerHour = Math.Round(r.RequestsPerHour, 2),
                    r.FirstRequest,
                    r.LastRequest,
                    r.StatusCodeDistribution,
                    r.UniqueClientIPs
                }).OrderByDescending(r => r.TotalRequests),
                TopApisByRequests = summary.TopApisByRequests.Take(10).Select(r => new
                {
                    r.ApiEndpoint,
                    r.Method,
                    r.TotalRequests
                }),
                TopApisByErrorRate = summary.TopApisByErrorRate.Take(10).Select(r => new
                {
                    r.ApiEndpoint,
                    r.Method,
                    ErrorRate = Math.Round(r.ErrorRate, 2),
                    r.TotalRequests
                }),
                SlowestApis = summary.SlowestApis.Take(10).Select(r => new
                {
                    r.ApiEndpoint,
                    r.Method,
                    AverageResponseTime = Math.Round(r.AverageResponseTime, 2),
                    r.TotalRequests
                })
            };
            
            var json = JsonConvert.SerializeObject(exportData, Formatting.Indented);
            await File.WriteAllTextAsync(filePath, json);
        }
        
        /// <summary>
        /// Export analysis results to HTML report
        /// </summary>
        public async Task ExportToHtmlAsync(OverallAnalysisSummary summary, string filePath)
        {
            var html = new StringBuilder();
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html>");
            html.AppendLine("<head>");
            html.AppendLine("<title>IIS Log Analysis Report</title>");
            html.AppendLine("<style>");
            html.AppendLine("body { font-family: Arial, sans-serif; margin: 20px; }");
            html.AppendLine("table { border-collapse: collapse; width: 100%; margin: 20px 0; }");
            html.AppendLine("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
            html.AppendLine("th { background-color: #f2f2f2; }");
            html.AppendLine("tr:nth-child(even) { background-color: #f9f9f9; }");
            html.AppendLine(".summary { background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }");
            html.AppendLine(".error { color: #d32f2f; }");
            html.AppendLine(".success { color: #388e3c; }");
            html.AppendLine("</style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            
            // Summary section
            html.AppendLine("<h1>IIS Log Analysis Report</h1>");
            html.AppendLine("<div class='summary'>");
            html.AppendLine($"<h2>Summary</h2>");
            html.AppendLine($"<p><strong>Total Log Entries:</strong> {summary.TotalLogEntries:N0}</p>");
            html.AppendLine($"<p><strong>Total API Endpoints:</strong> {summary.TotalApiEndpoints:N0}</p>");
            html.AppendLine($"<p><strong>Analysis Time:</strong> {summary.AnalysisStartTime:yyyy-MM-dd HH:mm:ss} - {summary.AnalysisEndTime:yyyy-MM-dd HH:mm:ss}</p>");
            html.AppendLine($"<p><strong>Log Time Span:</strong> {summary.LogTimeSpan.TotalHours:F1} hours</p>");
            html.AppendLine("</div>");
            
            // Top APIs table
            html.AppendLine("<h2>API Analysis Results</h2>");
            html.AppendLine("<table>");
            html.AppendLine("<tr>");
            html.AppendLine("<th>API Endpoint</th>");
            html.AppendLine("<th>Method</th>");
            html.AppendLine("<th>Total Requests</th>");
            html.AppendLine("<th>Success Rate</th>");
            html.AppendLine("<th>Error Rate</th>");
            html.AppendLine("<th>Avg Response Time (ms)</th>");
            html.AppendLine("<th>Unique Clients</th>");
            html.AppendLine("</tr>");
            
            foreach (var result in summary.ApiResults.OrderByDescending(r => r.TotalRequests).Take(50))
            {
                html.AppendLine("<tr>");
                html.AppendLine($"<td>{result.ApiEndpoint}</td>");
                html.AppendLine($"<td>{result.Method}</td>");
                html.AppendLine($"<td>{result.TotalRequests:N0}</td>");
                html.AppendLine($"<td class='success'>{result.SuccessRate:F1}%</td>");
                html.AppendLine($"<td class='error'>{result.ErrorRate:F1}%</td>");
                html.AppendLine($"<td>{result.AverageResponseTime:F0}</td>");
                html.AppendLine($"<td>{result.UniqueClientCount:N0}</td>");
                html.AppendLine("</tr>");
            }
            
            html.AppendLine("</table>");
            html.AppendLine("</body>");
            html.AppendLine("</html>");
            
            await File.WriteAllTextAsync(filePath, html.ToString());
        }
    }
}
