namespace IISLogAnalyzer.Forms
{
    partial class SettingsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tabControlSettings = new TabControl();
            this.tabPageAnalysis = new TabPage();
            this.tabPageExport = new TabPage();
            this.tabPagePerformance = new TabPage();
            this.tabPageColors = new TabPage();
            
            // Analysis tab controls
            this.groupBoxAnalysisDefaults = new GroupBox();
            this.labelTopApiCount = new Label();
            this.numericUpDownTopApiCount = new NumericUpDown();
            this.labelSlowThreshold = new Label();
            this.numericUpDownSlowThreshold = new NumericUpDown();
            this.labelErrorThreshold = new Label();
            this.numericUpDownErrorThreshold = new NumericUpDown();
            this.labelAutoRefresh = new Label();
            this.numericUpDownAutoRefresh = new NumericUpDown();
            this.lblAutoRefreshNote = new Label();
            this.labelMaxEntries = new Label();
            this.numericUpDownMaxEntries = new NumericUpDown();
            this.lblMaxEntriesNote = new Label();
            
            // Export tab controls
            this.groupBoxExportSettings = new GroupBox();
            this.labelDefaultExport = new Label();
            this.comboBoxDefaultExport = new ComboBox();
            this.checkBoxRememberDirectory = new CheckBox();
            
            // Performance tab controls
            this.groupBoxPerformanceSettings = new GroupBox();
            this.labelBatchSize = new Label();
            this.numericUpDownBatchSize = new NumericUpDown();
            this.checkBoxProgressReporting = new CheckBox();
            this.labelProgressInterval = new Label();
            this.numericUpDownProgressInterval = new NumericUpDown();
            this.checkBoxMemoryOptimization = new CheckBox();
            this.labelCacheSize = new Label();
            this.numericUpDownCacheSize = new NumericUpDown();
            
            // Colors tab controls
            this.groupBoxColorSettings = new GroupBox();
            this.labelSuccessColor = new Label();
            this.panelSuccessColor = new Panel();
            this.labelWarningColor = new Label();
            this.panelWarningColor = new Panel();
            this.labelErrorColor = new Label();
            this.panelErrorColor = new Panel();
            this.labelInfoColor = new Label();
            this.panelInfoColor = new Panel();
            this.labelSuccessThreshold = new Label();
            this.numericUpDownSuccessThreshold = new NumericUpDown();
            this.labelWarningThreshold = new Label();
            this.numericUpDownWarningThreshold = new NumericUpDown();
            this.btnTestColors = new Button();
            
            // Button controls
            this.panelButtons = new Panel();
            this.btnOK = new Button();
            this.btnCancel = new Button();
            this.btnApply = new Button();
            this.btnResetDefaults = new Button();
            
            this.tabControlSettings.SuspendLayout();
            this.tabPageAnalysis.SuspendLayout();
            this.tabPageExport.SuspendLayout();
            this.tabPagePerformance.SuspendLayout();
            this.tabPageColors.SuspendLayout();
            this.groupBoxAnalysisDefaults.SuspendLayout();
            this.groupBoxExportSettings.SuspendLayout();
            this.groupBoxPerformanceSettings.SuspendLayout();
            this.groupBoxColorSettings.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownTopApiCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownSlowThreshold)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownErrorThreshold)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownAutoRefresh)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMaxEntries)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownBatchSize)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownProgressInterval)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCacheSize)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownSuccessThreshold)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownWarningThreshold)).BeginInit();
            this.panelButtons.SuspendLayout();
            this.SuspendLayout();
            
            // 
            // tabControlSettings
            // 
            this.tabControlSettings.Controls.Add(this.tabPageAnalysis);
            this.tabControlSettings.Controls.Add(this.tabPageExport);
            this.tabControlSettings.Controls.Add(this.tabPagePerformance);
            this.tabControlSettings.Controls.Add(this.tabPageColors);
            this.tabControlSettings.Dock = DockStyle.Fill;
            this.tabControlSettings.Location = new Point(0, 0);
            this.tabControlSettings.Name = "tabControlSettings";
            this.tabControlSettings.SelectedIndex = 0;
            this.tabControlSettings.Size = new Size(600, 450);
            this.tabControlSettings.TabIndex = 0;
            
            // 
            // tabPageAnalysis
            // 
            this.tabPageAnalysis.Controls.Add(this.groupBoxAnalysisDefaults);
            this.tabPageAnalysis.Location = new Point(4, 24);
            this.tabPageAnalysis.Name = "tabPageAnalysis";
            this.tabPageAnalysis.Padding = new Padding(3);
            this.tabPageAnalysis.Size = new Size(592, 422);
            this.tabPageAnalysis.TabIndex = 0;
            this.tabPageAnalysis.Text = "Analysis";
            this.tabPageAnalysis.UseVisualStyleBackColor = true;
            
            // 
            // groupBoxAnalysisDefaults
            // 
            this.groupBoxAnalysisDefaults.Controls.Add(this.labelTopApiCount);
            this.groupBoxAnalysisDefaults.Controls.Add(this.numericUpDownTopApiCount);
            this.groupBoxAnalysisDefaults.Controls.Add(this.labelSlowThreshold);
            this.groupBoxAnalysisDefaults.Controls.Add(this.numericUpDownSlowThreshold);
            this.groupBoxAnalysisDefaults.Controls.Add(this.labelErrorThreshold);
            this.groupBoxAnalysisDefaults.Controls.Add(this.numericUpDownErrorThreshold);
            this.groupBoxAnalysisDefaults.Controls.Add(this.labelAutoRefresh);
            this.groupBoxAnalysisDefaults.Controls.Add(this.numericUpDownAutoRefresh);
            this.groupBoxAnalysisDefaults.Controls.Add(this.lblAutoRefreshNote);
            this.groupBoxAnalysisDefaults.Controls.Add(this.labelMaxEntries);
            this.groupBoxAnalysisDefaults.Controls.Add(this.numericUpDownMaxEntries);
            this.groupBoxAnalysisDefaults.Controls.Add(this.lblMaxEntriesNote);
            this.groupBoxAnalysisDefaults.Dock = DockStyle.Fill;
            this.groupBoxAnalysisDefaults.Location = new Point(3, 3);
            this.groupBoxAnalysisDefaults.Name = "groupBoxAnalysisDefaults";
            this.groupBoxAnalysisDefaults.Size = new Size(586, 416);
            this.groupBoxAnalysisDefaults.TabIndex = 0;
            this.groupBoxAnalysisDefaults.TabStop = false;
            this.groupBoxAnalysisDefaults.Text = "Analysis Defaults";
            
            // 
            // labelTopApiCount
            // 
            this.labelTopApiCount.AutoSize = true;
            this.labelTopApiCount.Location = new Point(15, 30);
            this.labelTopApiCount.Name = "labelTopApiCount";
            this.labelTopApiCount.Size = new Size(120, 15);
            this.labelTopApiCount.TabIndex = 0;
            this.labelTopApiCount.Text = "Default Top API Count:";
            
            // 
            // numericUpDownTopApiCount
            // 
            this.numericUpDownTopApiCount.Location = new Point(200, 28);
            this.numericUpDownTopApiCount.Maximum = new decimal(new int[] { 100, 0, 0, 0 });
            this.numericUpDownTopApiCount.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            this.numericUpDownTopApiCount.Name = "numericUpDownTopApiCount";
            this.numericUpDownTopApiCount.Size = new Size(80, 23);
            this.numericUpDownTopApiCount.TabIndex = 1;
            this.numericUpDownTopApiCount.Value = new decimal(new int[] { 10, 0, 0, 0 });
            
            // 
            // labelSlowThreshold
            // 
            this.labelSlowThreshold.AutoSize = true;
            this.labelSlowThreshold.Location = new Point(15, 65);
            this.labelSlowThreshold.Name = "labelSlowThreshold";
            this.labelSlowThreshold.Size = new Size(150, 15);
            this.labelSlowThreshold.TabIndex = 2;
            this.labelSlowThreshold.Text = "Slow API Threshold (ms):";
            
            // 
            // numericUpDownSlowThreshold
            // 
            this.numericUpDownSlowThreshold.Location = new Point(200, 63);
            this.numericUpDownSlowThreshold.Maximum = new decimal(new int[] { 60000, 0, 0, 0 });
            this.numericUpDownSlowThreshold.Minimum = new decimal(new int[] { 100, 0, 0, 0 });
            this.numericUpDownSlowThreshold.Name = "numericUpDownSlowThreshold";
            this.numericUpDownSlowThreshold.Size = new Size(80, 23);
            this.numericUpDownSlowThreshold.TabIndex = 3;
            this.numericUpDownSlowThreshold.Value = new decimal(new int[] { 1000, 0, 0, 0 });

            //
            // labelErrorThreshold
            //
            this.labelErrorThreshold.AutoSize = true;
            this.labelErrorThreshold.Location = new Point(15, 100);
            this.labelErrorThreshold.Name = "labelErrorThreshold";
            this.labelErrorThreshold.Size = new Size(140, 15);
            this.labelErrorThreshold.TabIndex = 4;
            this.labelErrorThreshold.Text = "Error Rate Threshold (%):";

            //
            // numericUpDownErrorThreshold
            //
            this.numericUpDownErrorThreshold.DecimalPlaces = 1;
            this.numericUpDownErrorThreshold.Location = new Point(200, 98);
            this.numericUpDownErrorThreshold.Maximum = new decimal(new int[] { 100, 0, 0, 0 });
            this.numericUpDownErrorThreshold.Name = "numericUpDownErrorThreshold";
            this.numericUpDownErrorThreshold.Size = new Size(80, 23);
            this.numericUpDownErrorThreshold.TabIndex = 5;
            this.numericUpDownErrorThreshold.Value = new decimal(new int[] { 50, 0, 0, 65536 });

            //
            // labelAutoRefresh
            //
            this.labelAutoRefresh.AutoSize = true;
            this.labelAutoRefresh.Location = new Point(15, 135);
            this.labelAutoRefresh.Name = "labelAutoRefresh";
            this.labelAutoRefresh.Size = new Size(160, 15);
            this.labelAutoRefresh.TabIndex = 6;
            this.labelAutoRefresh.Text = "Auto Refresh Interval (sec):";

            //
            // numericUpDownAutoRefresh
            //
            this.numericUpDownAutoRefresh.Location = new Point(200, 133);
            this.numericUpDownAutoRefresh.Maximum = new decimal(new int[] { 3600, 0, 0, 0 });
            this.numericUpDownAutoRefresh.Name = "numericUpDownAutoRefresh";
            this.numericUpDownAutoRefresh.Size = new Size(80, 23);
            this.numericUpDownAutoRefresh.TabIndex = 7;
            this.numericUpDownAutoRefresh.ValueChanged += new EventHandler(this.numericUpDownAutoRefresh_ValueChanged);

            //
            // lblAutoRefreshNote
            //
            this.lblAutoRefreshNote.AutoSize = true;
            this.lblAutoRefreshNote.ForeColor = Color.Gray;
            this.lblAutoRefreshNote.Location = new Point(290, 135);
            this.lblAutoRefreshNote.Name = "lblAutoRefreshNote";
            this.lblAutoRefreshNote.Size = new Size(120, 15);
            this.lblAutoRefreshNote.TabIndex = 8;
            this.lblAutoRefreshNote.Text = "Auto-refresh disabled";

            //
            // labelMaxEntries
            //
            this.labelMaxEntries.AutoSize = true;
            this.labelMaxEntries.Location = new Point(15, 170);
            this.labelMaxEntries.Name = "labelMaxEntries";
            this.labelMaxEntries.Size = new Size(170, 15);
            this.labelMaxEntries.TabIndex = 9;
            this.labelMaxEntries.Text = "Max Log Entries (0 = unlimited):";

            //
            // numericUpDownMaxEntries
            //
            this.numericUpDownMaxEntries.Location = new Point(200, 168);
            this.numericUpDownMaxEntries.Maximum = new decimal(new int[] { 1000000, 0, 0, 0 });
            this.numericUpDownMaxEntries.Name = "numericUpDownMaxEntries";
            this.numericUpDownMaxEntries.Size = new Size(100, 23);
            this.numericUpDownMaxEntries.TabIndex = 10;
            this.numericUpDownMaxEntries.ValueChanged += new EventHandler(this.numericUpDownMaxEntries_ValueChanged);

            //
            // lblMaxEntriesNote
            //
            this.lblMaxEntriesNote.AutoSize = true;
            this.lblMaxEntriesNote.ForeColor = Color.Green;
            this.lblMaxEntriesNote.Location = new Point(15, 195);
            this.lblMaxEntriesNote.Name = "lblMaxEntriesNote";
            this.lblMaxEntriesNote.Size = new Size(150, 15);
            this.lblMaxEntriesNote.TabIndex = 11;
            this.lblMaxEntriesNote.Text = "All entries will be processed";

            // Export Tab
            //
            // tabPageExport
            //
            this.tabPageExport.Controls.Add(this.groupBoxExportSettings);
            this.tabPageExport.Location = new Point(4, 24);
            this.tabPageExport.Name = "tabPageExport";
            this.tabPageExport.Padding = new Padding(3);
            this.tabPageExport.Size = new Size(592, 422);
            this.tabPageExport.TabIndex = 1;
            this.tabPageExport.Text = "Export";
            this.tabPageExport.UseVisualStyleBackColor = true;

            //
            // groupBoxExportSettings
            //
            this.groupBoxExportSettings.Controls.Add(this.labelDefaultExport);
            this.groupBoxExportSettings.Controls.Add(this.comboBoxDefaultExport);
            this.groupBoxExportSettings.Controls.Add(this.checkBoxRememberDirectory);
            this.groupBoxExportSettings.Dock = DockStyle.Fill;
            this.groupBoxExportSettings.Location = new Point(3, 3);
            this.groupBoxExportSettings.Name = "groupBoxExportSettings";
            this.groupBoxExportSettings.Size = new Size(586, 416);
            this.groupBoxExportSettings.TabIndex = 0;
            this.groupBoxExportSettings.TabStop = false;
            this.groupBoxExportSettings.Text = "Export Settings";

            //
            // labelDefaultExport
            //
            this.labelDefaultExport.AutoSize = true;
            this.labelDefaultExport.Location = new Point(15, 30);
            this.labelDefaultExport.Name = "labelDefaultExport";
            this.labelDefaultExport.Size = new Size(120, 15);
            this.labelDefaultExport.TabIndex = 0;
            this.labelDefaultExport.Text = "Default Export Format:";

            //
            // comboBoxDefaultExport
            //
            this.comboBoxDefaultExport.DropDownStyle = ComboBoxStyle.DropDownList;
            this.comboBoxDefaultExport.FormattingEnabled = true;
            this.comboBoxDefaultExport.Items.AddRange(new object[] { "CSV", "JSON", "HTML" });
            this.comboBoxDefaultExport.Location = new Point(150, 27);
            this.comboBoxDefaultExport.Name = "comboBoxDefaultExport";
            this.comboBoxDefaultExport.Size = new Size(100, 23);
            this.comboBoxDefaultExport.TabIndex = 1;

            //
            // checkBoxRememberDirectory
            //
            this.checkBoxRememberDirectory.AutoSize = true;
            this.checkBoxRememberDirectory.Location = new Point(15, 65);
            this.checkBoxRememberDirectory.Name = "checkBoxRememberDirectory";
            this.checkBoxRememberDirectory.Size = new Size(180, 19);
            this.checkBoxRememberDirectory.TabIndex = 2;
            this.checkBoxRememberDirectory.Text = "Remember last opened directory";
            this.checkBoxRememberDirectory.UseVisualStyleBackColor = true;

            // Performance Tab - simplified for space
            //
            // tabPagePerformance
            //
            this.tabPagePerformance.Controls.Add(this.groupBoxPerformanceSettings);
            this.tabPagePerformance.Location = new Point(4, 24);
            this.tabPagePerformance.Name = "tabPagePerformance";
            this.tabPagePerformance.Size = new Size(592, 422);
            this.tabPagePerformance.TabIndex = 2;
            this.tabPagePerformance.Text = "Performance";
            this.tabPagePerformance.UseVisualStyleBackColor = true;

            //
            // groupBoxPerformanceSettings
            //
            this.groupBoxPerformanceSettings.Controls.Add(this.labelBatchSize);
            this.groupBoxPerformanceSettings.Controls.Add(this.numericUpDownBatchSize);
            this.groupBoxPerformanceSettings.Controls.Add(this.checkBoxProgressReporting);
            this.groupBoxPerformanceSettings.Controls.Add(this.labelProgressInterval);
            this.groupBoxPerformanceSettings.Controls.Add(this.numericUpDownProgressInterval);
            this.groupBoxPerformanceSettings.Controls.Add(this.checkBoxMemoryOptimization);
            this.groupBoxPerformanceSettings.Controls.Add(this.labelCacheSize);
            this.groupBoxPerformanceSettings.Controls.Add(this.numericUpDownCacheSize);
            this.groupBoxPerformanceSettings.Dock = DockStyle.Fill;
            this.groupBoxPerformanceSettings.Location = new Point(3, 3);
            this.groupBoxPerformanceSettings.Name = "groupBoxPerformanceSettings";
            this.groupBoxPerformanceSettings.Size = new Size(586, 416);
            this.groupBoxPerformanceSettings.TabIndex = 0;
            this.groupBoxPerformanceSettings.TabStop = false;
            this.groupBoxPerformanceSettings.Text = "Performance Settings";

            // Add basic performance controls
            this.labelBatchSize.AutoSize = true;
            this.labelBatchSize.Location = new Point(15, 30);
            this.labelBatchSize.Name = "labelBatchSize";
            this.labelBatchSize.Size = new Size(65, 15);
            this.labelBatchSize.Text = "Batch Size:";

            this.numericUpDownBatchSize.Location = new Point(150, 28);
            this.numericUpDownBatchSize.Maximum = new decimal(new int[] { 10000, 0, 0, 0 });
            this.numericUpDownBatchSize.Minimum = new decimal(new int[] { 100, 0, 0, 0 });
            this.numericUpDownBatchSize.Name = "numericUpDownBatchSize";
            this.numericUpDownBatchSize.Size = new Size(80, 23);
            this.numericUpDownBatchSize.Value = new decimal(new int[] { 1000, 0, 0, 0 });

            this.checkBoxProgressReporting.AutoSize = true;
            this.checkBoxProgressReporting.Location = new Point(15, 65);
            this.checkBoxProgressReporting.Name = "checkBoxProgressReporting";
            this.checkBoxProgressReporting.Size = new Size(150, 19);
            this.checkBoxProgressReporting.Text = "Enable Progress Reporting";
            this.checkBoxProgressReporting.UseVisualStyleBackColor = true;
            this.checkBoxProgressReporting.CheckedChanged += new EventHandler(this.checkBoxProgressReporting_CheckedChanged);

            this.labelProgressInterval.AutoSize = true;
            this.labelProgressInterval.Location = new Point(30, 95);
            this.labelProgressInterval.Name = "labelProgressInterval";
            this.labelProgressInterval.Size = new Size(100, 15);
            this.labelProgressInterval.Text = "Progress Interval:";

            this.numericUpDownProgressInterval.Location = new Point(150, 93);
            this.numericUpDownProgressInterval.Maximum = new decimal(new int[] { 10000, 0, 0, 0 });
            this.numericUpDownProgressInterval.Minimum = new decimal(new int[] { 100, 0, 0, 0 });
            this.numericUpDownProgressInterval.Name = "numericUpDownProgressInterval";
            this.numericUpDownProgressInterval.Size = new Size(80, 23);
            this.numericUpDownProgressInterval.Value = new decimal(new int[] { 1000, 0, 0, 0 });

            this.checkBoxMemoryOptimization.AutoSize = true;
            this.checkBoxMemoryOptimization.Location = new Point(15, 130);
            this.checkBoxMemoryOptimization.Name = "checkBoxMemoryOptimization";
            this.checkBoxMemoryOptimization.Size = new Size(150, 19);
            this.checkBoxMemoryOptimization.Text = "Enable Memory Optimization";
            this.checkBoxMemoryOptimization.UseVisualStyleBackColor = true;

            this.labelCacheSize.AutoSize = true;
            this.labelCacheSize.Location = new Point(15, 165);
            this.labelCacheSize.Name = "labelCacheSize";
            this.labelCacheSize.Size = new Size(100, 15);
            this.labelCacheSize.Text = "Max Cache Size:";

            this.numericUpDownCacheSize.Location = new Point(150, 163);
            this.numericUpDownCacheSize.Maximum = new decimal(new int[] { 100000, 0, 0, 0 });
            this.numericUpDownCacheSize.Minimum = new decimal(new int[] { 1000, 0, 0, 0 });
            this.numericUpDownCacheSize.Name = "numericUpDownCacheSize";
            this.numericUpDownCacheSize.Size = new Size(80, 23);
            this.numericUpDownCacheSize.Value = new decimal(new int[] { 10000, 0, 0, 0 });

            // Colors Tab
            //
            // tabPageColors
            //
            this.tabPageColors.Controls.Add(this.groupBoxColorSettings);
            this.tabPageColors.Location = new Point(4, 24);
            this.tabPageColors.Name = "tabPageColors";
            this.tabPageColors.Size = new Size(592, 422);
            this.tabPageColors.TabIndex = 3;
            this.tabPageColors.Text = "Colors";
            this.tabPageColors.UseVisualStyleBackColor = true;

            //
            // groupBoxColorSettings
            //
            this.groupBoxColorSettings.Controls.Add(this.labelSuccessColor);
            this.groupBoxColorSettings.Controls.Add(this.panelSuccessColor);
            this.groupBoxColorSettings.Controls.Add(this.labelWarningColor);
            this.groupBoxColorSettings.Controls.Add(this.panelWarningColor);
            this.groupBoxColorSettings.Controls.Add(this.labelErrorColor);
            this.groupBoxColorSettings.Controls.Add(this.panelErrorColor);
            this.groupBoxColorSettings.Controls.Add(this.labelInfoColor);
            this.groupBoxColorSettings.Controls.Add(this.panelInfoColor);
            this.groupBoxColorSettings.Controls.Add(this.labelSuccessThreshold);
            this.groupBoxColorSettings.Controls.Add(this.numericUpDownSuccessThreshold);
            this.groupBoxColorSettings.Controls.Add(this.labelWarningThreshold);
            this.groupBoxColorSettings.Controls.Add(this.numericUpDownWarningThreshold);
            this.groupBoxColorSettings.Controls.Add(this.btnTestColors);
            this.groupBoxColorSettings.Dock = DockStyle.Fill;
            this.groupBoxColorSettings.Location = new Point(3, 3);
            this.groupBoxColorSettings.Name = "groupBoxColorSettings";
            this.groupBoxColorSettings.Size = new Size(586, 416);
            this.groupBoxColorSettings.TabIndex = 0;
            this.groupBoxColorSettings.TabStop = false;
            this.groupBoxColorSettings.Text = "Color Settings";

            // Add color controls
            this.labelSuccessColor.AutoSize = true;
            this.labelSuccessColor.Location = new Point(15, 30);
            this.labelSuccessColor.Name = "labelSuccessColor";
            this.labelSuccessColor.Size = new Size(85, 15);
            this.labelSuccessColor.Text = "Success Color:";

            this.panelSuccessColor.BorderStyle = BorderStyle.FixedSingle;
            this.panelSuccessColor.Location = new Point(120, 25);
            this.panelSuccessColor.Name = "panelSuccessColor";
            this.panelSuccessColor.Size = new Size(50, 25);
            this.panelSuccessColor.BackColor = Color.LightGreen;
            this.panelSuccessColor.Cursor = Cursors.Hand;
            this.panelSuccessColor.Click += new EventHandler(this.panelSuccessColor_Click);

            this.labelWarningColor.AutoSize = true;
            this.labelWarningColor.Location = new Point(15, 65);
            this.labelWarningColor.Name = "labelWarningColor";
            this.labelWarningColor.Size = new Size(85, 15);
            this.labelWarningColor.Text = "Warning Color:";

            this.panelWarningColor.BorderStyle = BorderStyle.FixedSingle;
            this.panelWarningColor.Location = new Point(120, 60);
            this.panelWarningColor.Name = "panelWarningColor";
            this.panelWarningColor.Size = new Size(50, 25);
            this.panelWarningColor.BackColor = Color.LightYellow;
            this.panelWarningColor.Cursor = Cursors.Hand;
            this.panelWarningColor.Click += new EventHandler(this.panelWarningColor_Click);

            this.labelErrorColor.AutoSize = true;
            this.labelErrorColor.Location = new Point(15, 100);
            this.labelErrorColor.Name = "labelErrorColor";
            this.labelErrorColor.Size = new Size(70, 15);
            this.labelErrorColor.Text = "Error Color:";

            this.panelErrorColor.BorderStyle = BorderStyle.FixedSingle;
            this.panelErrorColor.Location = new Point(120, 95);
            this.panelErrorColor.Name = "panelErrorColor";
            this.panelErrorColor.Size = new Size(50, 25);
            this.panelErrorColor.BackColor = Color.LightCoral;
            this.panelErrorColor.Cursor = Cursors.Hand;
            this.panelErrorColor.Click += new EventHandler(this.panelErrorColor_Click);

            this.labelInfoColor.AutoSize = true;
            this.labelInfoColor.Location = new Point(15, 135);
            this.labelInfoColor.Name = "labelInfoColor";
            this.labelInfoColor.Size = new Size(65, 15);
            this.labelInfoColor.Text = "Info Color:";

            this.panelInfoColor.BorderStyle = BorderStyle.FixedSingle;
            this.panelInfoColor.Location = new Point(120, 130);
            this.panelInfoColor.Name = "panelInfoColor";
            this.panelInfoColor.Size = new Size(50, 25);
            this.panelInfoColor.BackColor = Color.LightBlue;
            this.panelInfoColor.Cursor = Cursors.Hand;
            this.panelInfoColor.Click += new EventHandler(this.panelInfoColor_Click);

            this.labelSuccessThreshold.AutoSize = true;
            this.labelSuccessThreshold.Location = new Point(15, 170);
            this.labelSuccessThreshold.Name = "labelSuccessThreshold";
            this.labelSuccessThreshold.Size = new Size(140, 15);
            this.labelSuccessThreshold.Text = "Success Threshold (%):";

            this.numericUpDownSuccessThreshold.DecimalPlaces = 1;
            this.numericUpDownSuccessThreshold.Location = new Point(170, 168);
            this.numericUpDownSuccessThreshold.Maximum = new decimal(new int[] { 100, 0, 0, 0 });
            this.numericUpDownSuccessThreshold.Name = "numericUpDownSuccessThreshold";
            this.numericUpDownSuccessThreshold.Size = new Size(80, 23);
            this.numericUpDownSuccessThreshold.Value = new decimal(new int[] { 950, 0, 0, 65536 });

            this.labelWarningThreshold.AutoSize = true;
            this.labelWarningThreshold.Location = new Point(15, 205);
            this.labelWarningThreshold.Name = "labelWarningThreshold";
            this.labelWarningThreshold.Size = new Size(140, 15);
            this.labelWarningThreshold.Text = "Warning Threshold (%):";

            this.numericUpDownWarningThreshold.DecimalPlaces = 1;
            this.numericUpDownWarningThreshold.Location = new Point(170, 203);
            this.numericUpDownWarningThreshold.Maximum = new decimal(new int[] { 100, 0, 0, 0 });
            this.numericUpDownWarningThreshold.Name = "numericUpDownWarningThreshold";
            this.numericUpDownWarningThreshold.Size = new Size(80, 23);
            this.numericUpDownWarningThreshold.Value = new decimal(new int[] { 900, 0, 0, 65536 });

            this.btnTestColors.Location = new Point(15, 240);
            this.btnTestColors.Name = "btnTestColors";
            this.btnTestColors.Size = new Size(100, 30);
            this.btnTestColors.Text = "Test Colors";
            this.btnTestColors.UseVisualStyleBackColor = true;
            this.btnTestColors.Click += new EventHandler(this.btnTestColors_Click);

            //
            // panelButtons
            //
            this.panelButtons.Controls.Add(this.btnOK);
            this.panelButtons.Controls.Add(this.btnCancel);
            this.panelButtons.Controls.Add(this.btnApply);
            this.panelButtons.Controls.Add(this.btnResetDefaults);
            this.panelButtons.Dock = DockStyle.Bottom;
            this.panelButtons.Location = new Point(0, 450);
            this.panelButtons.Name = "panelButtons";
            this.panelButtons.Size = new Size(600, 50);
            this.panelButtons.TabIndex = 1;

            //
            // btnOK
            //
            this.btnOK.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.btnOK.Location = new Point(350, 15);
            this.btnOK.Name = "btnOK";
            this.btnOK.Size = new Size(75, 25);
            this.btnOK.TabIndex = 0;
            this.btnOK.Text = "OK";
            this.btnOK.UseVisualStyleBackColor = true;
            this.btnOK.Click += new EventHandler(this.btnOK_Click);

            //
            // btnCancel
            //
            this.btnCancel.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.btnCancel.DialogResult = DialogResult.Cancel;
            this.btnCancel.Location = new Point(435, 15);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(75, 25);
            this.btnCancel.TabIndex = 1;
            this.btnCancel.Text = "Cancel";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            //
            // btnApply
            //
            this.btnApply.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.btnApply.Location = new Point(520, 15);
            this.btnApply.Name = "btnApply";
            this.btnApply.Size = new Size(75, 25);
            this.btnApply.TabIndex = 2;
            this.btnApply.Text = "Apply";
            this.btnApply.UseVisualStyleBackColor = true;
            this.btnApply.Click += new EventHandler(this.btnApply_Click);

            //
            // btnResetDefaults
            //
            this.btnResetDefaults.Anchor = AnchorStyles.Bottom | AnchorStyles.Left;
            this.btnResetDefaults.Location = new Point(15, 15);
            this.btnResetDefaults.Name = "btnResetDefaults";
            this.btnResetDefaults.Size = new Size(100, 25);
            this.btnResetDefaults.TabIndex = 3;
            this.btnResetDefaults.Text = "Reset Defaults";
            this.btnResetDefaults.UseVisualStyleBackColor = true;
            this.btnResetDefaults.Click += new EventHandler(this.btnResetDefaults_Click);

            //
            // SettingsForm
            //
            this.AcceptButton = this.btnOK;
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.CancelButton = this.btnCancel;
            this.ClientSize = new Size(600, 500);
            this.Controls.Add(this.tabControlSettings);
            this.Controls.Add(this.panelButtons);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "SettingsForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "Application Settings";

            this.tabControlSettings.ResumeLayout(false);
            this.tabPageAnalysis.ResumeLayout(false);
            this.tabPageExport.ResumeLayout(false);
            this.tabPagePerformance.ResumeLayout(false);
            this.tabPageColors.ResumeLayout(false);
            this.groupBoxAnalysisDefaults.ResumeLayout(false);
            this.groupBoxAnalysisDefaults.PerformLayout();
            this.groupBoxExportSettings.ResumeLayout(false);
            this.groupBoxExportSettings.PerformLayout();
            this.groupBoxPerformanceSettings.ResumeLayout(false);
            this.groupBoxPerformanceSettings.PerformLayout();
            this.groupBoxColorSettings.ResumeLayout(false);
            this.groupBoxColorSettings.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownTopApiCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownSlowThreshold)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownErrorThreshold)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownAutoRefresh)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMaxEntries)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownBatchSize)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownProgressInterval)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownCacheSize)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownSuccessThreshold)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownWarningThreshold)).EndInit();
            this.panelButtons.ResumeLayout(false);
            this.ResumeLayout(false);
        }

        #endregion

        private TabControl tabControlSettings;
        private TabPage tabPageAnalysis;
        private TabPage tabPageExport;
        private TabPage tabPagePerformance;
        private TabPage tabPageColors;
        private GroupBox groupBoxAnalysisDefaults;
        private Label labelTopApiCount;
        private NumericUpDown numericUpDownTopApiCount;
        private Label labelSlowThreshold;
        private NumericUpDown numericUpDownSlowThreshold;
        private Label labelErrorThreshold;
        private NumericUpDown numericUpDownErrorThreshold;
        private Label labelAutoRefresh;
        private NumericUpDown numericUpDownAutoRefresh;
        private Label lblAutoRefreshNote;
        private Label labelMaxEntries;
        private NumericUpDown numericUpDownMaxEntries;
        private Label lblMaxEntriesNote;
        private GroupBox groupBoxExportSettings;
        private Label labelDefaultExport;
        private ComboBox comboBoxDefaultExport;
        private CheckBox checkBoxRememberDirectory;
        private GroupBox groupBoxPerformanceSettings;
        private Label labelBatchSize;
        private NumericUpDown numericUpDownBatchSize;
        private CheckBox checkBoxProgressReporting;
        private Label labelProgressInterval;
        private NumericUpDown numericUpDownProgressInterval;
        private CheckBox checkBoxMemoryOptimization;
        private Label labelCacheSize;
        private NumericUpDown numericUpDownCacheSize;
        private GroupBox groupBoxColorSettings;
        private Label labelSuccessColor;
        private Panel panelSuccessColor;
        private Label labelWarningColor;
        private Panel panelWarningColor;
        private Label labelErrorColor;
        private Panel panelErrorColor;
        private Label labelInfoColor;
        private Panel panelInfoColor;
        private Label labelSuccessThreshold;
        private NumericUpDown numericUpDownSuccessThreshold;
        private Label labelWarningThreshold;
        private NumericUpDown numericUpDownWarningThreshold;
        private Button btnTestColors;
        private Panel panelButtons;
        private Button btnOK;
        private Button btnCancel;
        private Button btnApply;
        private Button btnResetDefaults;
    }
}
