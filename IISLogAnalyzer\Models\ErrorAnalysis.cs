using System;
using System.Collections.Generic;
using System.Linq;

namespace IISLogAnalyzer.Models
{
    /// <summary>
    /// Error analysis data models
    /// </summary>
    public class ErrorAnalysis
    {
        public ErrorCodeAnalysis ErrorCodeAnalysis { get; set; } = new();
        public ErrorTrendAnalysis ErrorTrends { get; set; } = new();
        public List<ProblematicEndpoint> ProblematicEndpoints { get; set; } = new();
        public ClientVsServerErrorAnalysis ClientVsServerErrors { get; set; } = new();
        public List<ErrorEvent> RecentErrors { get; set; } = new();
    }

    /// <summary>
    /// Detailed error code breakdown
    /// </summary>
    public class ErrorCodeAnalysis
    {
        public Dictionary<int, ErrorCodeInfo> ErrorCodes { get; set; } = new();
        public int TotalErrors { get; set; }
        public double OverallErrorRate { get; set; }
        public List<ErrorCodeInfo> MostCommonErrors { get; set; } = new();
        public Dictionary<string, Dictionary<int, int>> ErrorsByEndpoint { get; set; } = new();
    }

    /// <summary>
    /// Information about specific error codes
    /// </summary>
    public class ErrorCodeInfo
    {
        public int StatusCode { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty; // Client Error, Server Error, etc.
        public int Count { get; set; }
        public double Percentage { get; set; }
        public List<string> AffectedEndpoints { get; set; } = new();
        public List<string> CommonCauses { get; set; } = new();
        public string Recommendation { get; set; } = string.Empty;
        public ErrorSeverity Severity { get; set; }
    }

    /// <summary>
    /// Error severity levels
    /// </summary>
    public enum ErrorSeverity
    {
        Low,
        Medium,
        High,
        Critical
    }

    /// <summary>
    /// Error trends over time
    /// </summary>
    public class ErrorTrendAnalysis
    {
        public Dictionary<DateTime, int> HourlyErrorCounts { get; set; } = new();
        public Dictionary<DateTime, double> HourlyErrorRates { get; set; } = new();
        public Dictionary<DateTime, Dictionary<int, int>> ErrorCodeTrends { get; set; } = new();
        public List<ErrorSpike> ErrorSpikes { get; set; } = new();
        public TrendDirection OverallTrend { get; set; }
        public double TrendPercentage { get; set; }
    }

    /// <summary>
    /// Trend direction
    /// </summary>
    public enum TrendDirection
    {
        Improving,
        Stable,
        Worsening
    }

    /// <summary>
    /// Error spike detection
    /// </summary>
    public class ErrorSpike
    {
        public DateTime TimeStamp { get; set; }
        public int ErrorCount { get; set; }
        public double ErrorRate { get; set; }
        public double SpikeMultiplier { get; set; } // How many times higher than average
        public List<int> DominantErrorCodes { get; set; } = new();
        public List<string> AffectedEndpoints { get; set; } = new();
        public string PossibleCause { get; set; } = string.Empty;
    }

    /// <summary>
    /// Problematic endpoint analysis
    /// </summary>
    public class ProblematicEndpoint
    {
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public int TotalRequests { get; set; }
        public int ErrorCount { get; set; }
        public double ErrorRate { get; set; }
        public Dictionary<int, int> ErrorCodeDistribution { get; set; } = new();
        public List<ErrorEvent> RecentErrors { get; set; } = new();
        public ErrorTrend Trend { get; set; } = new();
        public List<string> CommonErrorPatterns { get; set; } = new();
        public string Recommendation { get; set; } = string.Empty;
        public int PriorityScore { get; set; } // 1-100
    }

    /// <summary>
    /// Error trend for specific endpoint
    /// </summary>
    public class ErrorTrend
    {
        public TrendDirection Direction { get; set; }
        public double ChangePercentage { get; set; }
        public Dictionary<DateTime, double> ErrorRateOverTime { get; set; } = new();
    }

    /// <summary>
    /// Client vs Server error analysis
    /// </summary>
    public class ClientVsServerErrorAnalysis
    {
        public ClientErrorAnalysis ClientErrors { get; set; } = new();
        public ServerErrorAnalysis ServerErrors { get; set; } = new();
        public double ClientErrorPercentage { get; set; }
        public double ServerErrorPercentage { get; set; }
        public Dictionary<DateTime, int> ClientErrorTrend { get; set; } = new();
        public Dictionary<DateTime, int> ServerErrorTrend { get; set; } = new();
    }

    /// <summary>
    /// Client error (4xx) analysis
    /// </summary>
    public class ClientErrorAnalysis
    {
        public int TotalClientErrors { get; set; }
        public int NotFoundErrors { get; set; } // 404
        public int UnauthorizedErrors { get; set; } // 401
        public int ForbiddenErrors { get; set; } // 403
        public int BadRequestErrors { get; set; } // 400
        public int OtherClientErrors { get; set; }
        public Dictionary<int, int> ClientErrorDistribution { get; set; } = new();
        public List<string> MostRequestedMissingPages { get; set; } = new();
        public List<string> CommonBadRequestPatterns { get; set; } = new();
    }

    /// <summary>
    /// Server error (5xx) analysis
    /// </summary>
    public class ServerErrorAnalysis
    {
        public int TotalServerErrors { get; set; }
        public int InternalServerErrors { get; set; } // 500
        public int BadGatewayErrors { get; set; } // 502
        public int ServiceUnavailableErrors { get; set; } // 503
        public int GatewayTimeoutErrors { get; set; } // 504
        public int OtherServerErrors { get; set; }
        public Dictionary<int, int> ServerErrorDistribution { get; set; } = new();
        public List<string> MostProblematicEndpoints { get; set; } = new();
        public List<DateTime> ErrorSpikeTimes { get; set; } = new();
    }

    /// <summary>
    /// Individual error event
    /// </summary>
    public class ErrorEvent
    {
        public DateTime TimeStamp { get; set; }
        public string ClientIP { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public string Endpoint { get; set; } = string.Empty;
        public string QueryString { get; set; } = string.Empty;
        public int StatusCode { get; set; }
        public int SubStatusCode { get; set; }
        public int Win32StatusCode { get; set; }
        public string UserAgent { get; set; } = string.Empty;
        public string Referer { get; set; } = string.Empty;
        public int ResponseTime { get; set; }
        public long BytesSent { get; set; }
        public string ErrorCategory { get; set; } = string.Empty;
        public string PossibleCause { get; set; } = string.Empty;
    }
}
