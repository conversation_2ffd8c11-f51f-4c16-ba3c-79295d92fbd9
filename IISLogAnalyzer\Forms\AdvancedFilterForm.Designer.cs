namespace IISLogAnalyzer.Forms
{
    partial class AdvancedFilterForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.groupBoxEndpoint = new GroupBox();
            this.textBoxApiEndpoint = new TextBox();
            this.labelApiEndpoint = new Label();
            
            this.groupBoxMethods = new GroupBox();
            this.flowLayoutPanelMethods = new FlowLayoutPanel();
            
            this.groupBoxDateRange = new GroupBox();
            this.checkBoxDateRange = new CheckBox();
            this.dateTimePickerFrom = new DateTimePicker();
            this.dateTimePickerTo = new DateTimePicker();
            this.labelDateFrom = new Label();
            this.labelDateTo = new Label();
            
            this.groupBoxStatusCode = new GroupBox();
            this.comboBoxStatusCode = new ComboBox();
            this.labelStatusFrom = new Label();
            this.labelStatusTo = new Label();
            this.numericUpDownStatusFrom = new NumericUpDown();
            this.numericUpDownStatusTo = new NumericUpDown();
            
            this.groupBoxResponseTime = new GroupBox();
            this.checkBoxResponseTime = new CheckBox();
            this.numericUpDownMinResponseTime = new NumericUpDown();
            this.numericUpDownMaxResponseTime = new NumericUpDown();
            this.labelMinResponseTime = new Label();
            this.labelMaxResponseTime = new Label();
            
            this.groupBoxRequests = new GroupBox();
            this.checkBoxMinRequests = new CheckBox();
            this.numericUpDownMinRequests = new NumericUpDown();
            this.checkBoxMinClients = new CheckBox();
            this.numericUpDownMinClients = new NumericUpDown();
            this.labelMinRequests = new Label();
            this.labelMinClients = new Label();
            
            this.btnApply = new Button();
            this.btnReset = new Button();
            this.btnCancel = new Button();
            
            this.groupBoxEndpoint.SuspendLayout();
            this.groupBoxMethods.SuspendLayout();
            this.groupBoxDateRange.SuspendLayout();
            this.groupBoxStatusCode.SuspendLayout();
            this.groupBoxResponseTime.SuspendLayout();
            this.groupBoxRequests.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownStatusFrom)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownStatusTo)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMinResponseTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMaxResponseTime)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMinRequests)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMinClients)).BeginInit();
            this.SuspendLayout();
            
            // 
            // groupBoxEndpoint
            // 
            this.groupBoxEndpoint.Controls.Add(this.textBoxApiEndpoint);
            this.groupBoxEndpoint.Controls.Add(this.labelApiEndpoint);
            this.groupBoxEndpoint.Location = new Point(12, 12);
            this.groupBoxEndpoint.Name = "groupBoxEndpoint";
            this.groupBoxEndpoint.Size = new Size(460, 60);
            this.groupBoxEndpoint.TabIndex = 0;
            this.groupBoxEndpoint.TabStop = false;
            this.groupBoxEndpoint.Text = "API Endpoint Filter";
            
            // 
            // labelApiEndpoint
            // 
            this.labelApiEndpoint.AutoSize = true;
            this.labelApiEndpoint.Location = new Point(10, 25);
            this.labelApiEndpoint.Name = "labelApiEndpoint";
            this.labelApiEndpoint.Size = new Size(60, 15);
            this.labelApiEndpoint.TabIndex = 0;
            this.labelApiEndpoint.Text = "Contains:";
            
            // 
            // textBoxApiEndpoint
            // 
            this.textBoxApiEndpoint.Location = new Point(76, 22);
            this.textBoxApiEndpoint.Name = "textBoxApiEndpoint";
            this.textBoxApiEndpoint.PlaceholderText = "e.g., /api/users";
            this.textBoxApiEndpoint.Size = new Size(370, 23);
            this.textBoxApiEndpoint.TabIndex = 1;
            
            // 
            // groupBoxMethods
            // 
            this.groupBoxMethods.Controls.Add(this.flowLayoutPanelMethods);
            this.groupBoxMethods.Location = new Point(12, 78);
            this.groupBoxMethods.Name = "groupBoxMethods";
            this.groupBoxMethods.Size = new Size(460, 80);
            this.groupBoxMethods.TabIndex = 1;
            this.groupBoxMethods.TabStop = false;
            this.groupBoxMethods.Text = "HTTP Methods";
            
            // 
            // flowLayoutPanelMethods
            // 
            this.flowLayoutPanelMethods.Dock = DockStyle.Fill;
            this.flowLayoutPanelMethods.Location = new Point(3, 19);
            this.flowLayoutPanelMethods.Name = "flowLayoutPanelMethods";
            this.flowLayoutPanelMethods.Size = new Size(454, 58);
            this.flowLayoutPanelMethods.TabIndex = 0;
            
            // 
            // groupBoxDateRange
            // 
            this.groupBoxDateRange.Controls.Add(this.checkBoxDateRange);
            this.groupBoxDateRange.Controls.Add(this.labelDateFrom);
            this.groupBoxDateRange.Controls.Add(this.dateTimePickerFrom);
            this.groupBoxDateRange.Controls.Add(this.labelDateTo);
            this.groupBoxDateRange.Controls.Add(this.dateTimePickerTo);
            this.groupBoxDateRange.Location = new Point(12, 164);
            this.groupBoxDateRange.Name = "groupBoxDateRange";
            this.groupBoxDateRange.Size = new Size(460, 80);
            this.groupBoxDateRange.TabIndex = 2;
            this.groupBoxDateRange.TabStop = false;
            this.groupBoxDateRange.Text = "Date Range";
            
            // 
            // checkBoxDateRange
            // 
            this.checkBoxDateRange.AutoSize = true;
            this.checkBoxDateRange.Location = new Point(10, 25);
            this.checkBoxDateRange.Name = "checkBoxDateRange";
            this.checkBoxDateRange.Size = new Size(130, 19);
            this.checkBoxDateRange.TabIndex = 0;
            this.checkBoxDateRange.Text = "Filter by date range";
            this.checkBoxDateRange.UseVisualStyleBackColor = true;
            this.checkBoxDateRange.CheckedChanged += new EventHandler(this.checkBoxDateRange_CheckedChanged);
            
            // 
            // labelDateFrom
            // 
            this.labelDateFrom.AutoSize = true;
            this.labelDateFrom.Location = new Point(10, 52);
            this.labelDateFrom.Name = "labelDateFrom";
            this.labelDateFrom.Size = new Size(38, 15);
            this.labelDateFrom.TabIndex = 1;
            this.labelDateFrom.Text = "From:";
            
            // 
            // dateTimePickerFrom
            // 
            this.dateTimePickerFrom.Enabled = false;
            this.dateTimePickerFrom.Format = DateTimePickerFormat.Short;
            this.dateTimePickerFrom.Location = new Point(54, 48);
            this.dateTimePickerFrom.Name = "dateTimePickerFrom";
            this.dateTimePickerFrom.Size = new Size(120, 23);
            this.dateTimePickerFrom.TabIndex = 2;
            
            // 
            // labelDateTo
            // 
            this.labelDateTo.AutoSize = true;
            this.labelDateTo.Location = new Point(190, 52);
            this.labelDateTo.Name = "labelDateTo";
            this.labelDateTo.Size = new Size(22, 15);
            this.labelDateTo.TabIndex = 3;
            this.labelDateTo.Text = "To:";
            
            // 
            // dateTimePickerTo
            // 
            this.dateTimePickerTo.Enabled = false;
            this.dateTimePickerTo.Format = DateTimePickerFormat.Short;
            this.dateTimePickerTo.Location = new Point(218, 48);
            this.dateTimePickerTo.Name = "dateTimePickerTo";
            this.dateTimePickerTo.Size = new Size(120, 23);
            this.dateTimePickerTo.TabIndex = 4;

            //
            // groupBoxStatusCode
            //
            this.groupBoxStatusCode.Controls.Add(this.comboBoxStatusCode);
            this.groupBoxStatusCode.Controls.Add(this.labelStatusFrom);
            this.groupBoxStatusCode.Controls.Add(this.numericUpDownStatusFrom);
            this.groupBoxStatusCode.Controls.Add(this.labelStatusTo);
            this.groupBoxStatusCode.Controls.Add(this.numericUpDownStatusTo);
            this.groupBoxStatusCode.Location = new Point(12, 250);
            this.groupBoxStatusCode.Name = "groupBoxStatusCode";
            this.groupBoxStatusCode.Size = new Size(460, 80);
            this.groupBoxStatusCode.TabIndex = 3;
            this.groupBoxStatusCode.TabStop = false;
            this.groupBoxStatusCode.Text = "Status Code Filter";

            //
            // comboBoxStatusCode
            //
            this.comboBoxStatusCode.DropDownStyle = ComboBoxStyle.DropDownList;
            this.comboBoxStatusCode.FormattingEnabled = true;
            this.comboBoxStatusCode.Location = new Point(10, 25);
            this.comboBoxStatusCode.Name = "comboBoxStatusCode";
            this.comboBoxStatusCode.Size = new Size(440, 23);
            this.comboBoxStatusCode.TabIndex = 0;
            this.comboBoxStatusCode.SelectedIndexChanged += new EventHandler(this.comboBoxStatusCode_SelectedIndexChanged);

            //
            // labelStatusFrom
            //
            this.labelStatusFrom.AutoSize = true;
            this.labelStatusFrom.Location = new Point(10, 55);
            this.labelStatusFrom.Name = "labelStatusFrom";
            this.labelStatusFrom.Size = new Size(38, 15);
            this.labelStatusFrom.TabIndex = 1;
            this.labelStatusFrom.Text = "From:";
            this.labelStatusFrom.Visible = false;

            //
            // numericUpDownStatusFrom
            //
            this.numericUpDownStatusFrom.Location = new Point(54, 52);
            this.numericUpDownStatusFrom.Maximum = new decimal(new int[] { 599, 0, 0, 0 });
            this.numericUpDownStatusFrom.Minimum = new decimal(new int[] { 100, 0, 0, 0 });
            this.numericUpDownStatusFrom.Name = "numericUpDownStatusFrom";
            this.numericUpDownStatusFrom.Size = new Size(80, 23);
            this.numericUpDownStatusFrom.TabIndex = 2;
            this.numericUpDownStatusFrom.Value = new decimal(new int[] { 200, 0, 0, 0 });
            this.numericUpDownStatusFrom.Visible = false;

            //
            // labelStatusTo
            //
            this.labelStatusTo.AutoSize = true;
            this.labelStatusTo.Location = new Point(150, 55);
            this.labelStatusTo.Name = "labelStatusTo";
            this.labelStatusTo.Size = new Size(22, 15);
            this.labelStatusTo.TabIndex = 3;
            this.labelStatusTo.Text = "To:";
            this.labelStatusTo.Visible = false;

            //
            // numericUpDownStatusTo
            //
            this.numericUpDownStatusTo.Location = new Point(178, 52);
            this.numericUpDownStatusTo.Maximum = new decimal(new int[] { 599, 0, 0, 0 });
            this.numericUpDownStatusTo.Minimum = new decimal(new int[] { 100, 0, 0, 0 });
            this.numericUpDownStatusTo.Name = "numericUpDownStatusTo";
            this.numericUpDownStatusTo.Size = new Size(80, 23);
            this.numericUpDownStatusTo.TabIndex = 4;
            this.numericUpDownStatusTo.Value = new decimal(new int[] { 299, 0, 0, 0 });
            this.numericUpDownStatusTo.Visible = false;

            //
            // groupBoxResponseTime
            //
            this.groupBoxResponseTime.Controls.Add(this.checkBoxResponseTime);
            this.groupBoxResponseTime.Controls.Add(this.labelMinResponseTime);
            this.groupBoxResponseTime.Controls.Add(this.numericUpDownMinResponseTime);
            this.groupBoxResponseTime.Controls.Add(this.labelMaxResponseTime);
            this.groupBoxResponseTime.Controls.Add(this.numericUpDownMaxResponseTime);
            this.groupBoxResponseTime.Location = new Point(12, 336);
            this.groupBoxResponseTime.Name = "groupBoxResponseTime";
            this.groupBoxResponseTime.Size = new Size(460, 80);
            this.groupBoxResponseTime.TabIndex = 4;
            this.groupBoxResponseTime.TabStop = false;
            this.groupBoxResponseTime.Text = "Response Time Filter (ms)";

            //
            // checkBoxResponseTime
            //
            this.checkBoxResponseTime.AutoSize = true;
            this.checkBoxResponseTime.Location = new Point(10, 25);
            this.checkBoxResponseTime.Name = "checkBoxResponseTime";
            this.checkBoxResponseTime.Size = new Size(160, 19);
            this.checkBoxResponseTime.TabIndex = 0;
            this.checkBoxResponseTime.Text = "Filter by response time";
            this.checkBoxResponseTime.UseVisualStyleBackColor = true;
            this.checkBoxResponseTime.CheckedChanged += new EventHandler(this.checkBoxResponseTime_CheckedChanged);

            //
            // labelMinResponseTime
            //
            this.labelMinResponseTime.AutoSize = true;
            this.labelMinResponseTime.Location = new Point(10, 52);
            this.labelMinResponseTime.Name = "labelMinResponseTime";
            this.labelMinResponseTime.Size = new Size(31, 15);
            this.labelMinResponseTime.TabIndex = 1;
            this.labelMinResponseTime.Text = "Min:";

            //
            // numericUpDownMinResponseTime
            //
            this.numericUpDownMinResponseTime.Enabled = false;
            this.numericUpDownMinResponseTime.Location = new Point(47, 48);
            this.numericUpDownMinResponseTime.Maximum = new decimal(new int[] { 60000, 0, 0, 0 });
            this.numericUpDownMinResponseTime.Name = "numericUpDownMinResponseTime";
            this.numericUpDownMinResponseTime.Size = new Size(80, 23);
            this.numericUpDownMinResponseTime.TabIndex = 2;

            //
            // labelMaxResponseTime
            //
            this.labelMaxResponseTime.AutoSize = true;
            this.labelMaxResponseTime.Location = new Point(140, 52);
            this.labelMaxResponseTime.Name = "labelMaxResponseTime";
            this.labelMaxResponseTime.Size = new Size(34, 15);
            this.labelMaxResponseTime.TabIndex = 3;
            this.labelMaxResponseTime.Text = "Max:";

            //
            // numericUpDownMaxResponseTime
            //
            this.numericUpDownMaxResponseTime.Enabled = false;
            this.numericUpDownMaxResponseTime.Location = new Point(180, 48);
            this.numericUpDownMaxResponseTime.Maximum = new decimal(new int[] { 60000, 0, 0, 0 });
            this.numericUpDownMaxResponseTime.Name = "numericUpDownMaxResponseTime";
            this.numericUpDownMaxResponseTime.Size = new Size(80, 23);
            this.numericUpDownMaxResponseTime.TabIndex = 4;
            this.numericUpDownMaxResponseTime.Value = new decimal(new int[] { 10000, 0, 0, 0 });

            //
            // groupBoxRequests
            //
            this.groupBoxRequests.Controls.Add(this.checkBoxMinRequests);
            this.groupBoxRequests.Controls.Add(this.labelMinRequests);
            this.groupBoxRequests.Controls.Add(this.numericUpDownMinRequests);
            this.groupBoxRequests.Controls.Add(this.checkBoxMinClients);
            this.groupBoxRequests.Controls.Add(this.labelMinClients);
            this.groupBoxRequests.Controls.Add(this.numericUpDownMinClients);
            this.groupBoxRequests.Location = new Point(12, 422);
            this.groupBoxRequests.Name = "groupBoxRequests";
            this.groupBoxRequests.Size = new Size(460, 100);
            this.groupBoxRequests.TabIndex = 5;
            this.groupBoxRequests.TabStop = false;
            this.groupBoxRequests.Text = "Volume Filters";

            //
            // checkBoxMinRequests
            //
            this.checkBoxMinRequests.AutoSize = true;
            this.checkBoxMinRequests.Location = new Point(10, 25);
            this.checkBoxMinRequests.Name = "checkBoxMinRequests";
            this.checkBoxMinRequests.Size = new Size(140, 19);
            this.checkBoxMinRequests.TabIndex = 0;
            this.checkBoxMinRequests.Text = "Minimum requests:";
            this.checkBoxMinRequests.UseVisualStyleBackColor = true;
            this.checkBoxMinRequests.CheckedChanged += new EventHandler(this.checkBoxMinRequests_CheckedChanged);

            //
            // labelMinRequests
            //
            this.labelMinRequests.AutoSize = true;
            this.labelMinRequests.Location = new Point(156, 26);
            this.labelMinRequests.Name = "labelMinRequests";
            this.labelMinRequests.Size = new Size(0, 15);
            this.labelMinRequests.TabIndex = 1;

            //
            // numericUpDownMinRequests
            //
            this.numericUpDownMinRequests.Enabled = false;
            this.numericUpDownMinRequests.Location = new Point(156, 23);
            this.numericUpDownMinRequests.Maximum = new decimal(new int[] { 100000, 0, 0, 0 });
            this.numericUpDownMinRequests.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            this.numericUpDownMinRequests.Name = "numericUpDownMinRequests";
            this.numericUpDownMinRequests.Size = new Size(80, 23);
            this.numericUpDownMinRequests.TabIndex = 2;
            this.numericUpDownMinRequests.Value = new decimal(new int[] { 1, 0, 0, 0 });

            //
            // checkBoxMinClients
            //
            this.checkBoxMinClients.AutoSize = true;
            this.checkBoxMinClients.Location = new Point(10, 55);
            this.checkBoxMinClients.Name = "checkBoxMinClients";
            this.checkBoxMinClients.Size = new Size(140, 19);
            this.checkBoxMinClients.TabIndex = 3;
            this.checkBoxMinClients.Text = "Minimum unique clients:";
            this.checkBoxMinClients.UseVisualStyleBackColor = true;
            this.checkBoxMinClients.CheckedChanged += new EventHandler(this.checkBoxMinClients_CheckedChanged);

            //
            // labelMinClients
            //
            this.labelMinClients.AutoSize = true;
            this.labelMinClients.Location = new Point(156, 56);
            this.labelMinClients.Name = "labelMinClients";
            this.labelMinClients.Size = new Size(0, 15);
            this.labelMinClients.TabIndex = 4;

            //
            // numericUpDownMinClients
            //
            this.numericUpDownMinClients.Enabled = false;
            this.numericUpDownMinClients.Location = new Point(156, 53);
            this.numericUpDownMinClients.Maximum = new decimal(new int[] { 10000, 0, 0, 0 });
            this.numericUpDownMinClients.Minimum = new decimal(new int[] { 1, 0, 0, 0 });
            this.numericUpDownMinClients.Name = "numericUpDownMinClients";
            this.numericUpDownMinClients.Size = new Size(80, 23);
            this.numericUpDownMinClients.TabIndex = 5;
            this.numericUpDownMinClients.Value = new decimal(new int[] { 1, 0, 0, 0 });

            //
            // btnApply
            //
            this.btnApply.Location = new Point(235, 540);
            this.btnApply.Name = "btnApply";
            this.btnApply.Size = new Size(75, 30);
            this.btnApply.TabIndex = 6;
            this.btnApply.Text = "Apply";
            this.btnApply.UseVisualStyleBackColor = true;
            this.btnApply.Click += new EventHandler(this.btnApply_Click);

            //
            // btnReset
            //
            this.btnReset.Location = new Point(316, 540);
            this.btnReset.Name = "btnReset";
            this.btnReset.Size = new Size(75, 30);
            this.btnReset.TabIndex = 7;
            this.btnReset.Text = "Reset";
            this.btnReset.UseVisualStyleBackColor = true;
            this.btnReset.Click += new EventHandler(this.btnReset_Click);

            //
            // btnCancel
            //
            this.btnCancel.Location = new Point(397, 540);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new Size(75, 30);
            this.btnCancel.TabIndex = 8;
            this.btnCancel.Text = "Cancel";
            this.btnCancel.UseVisualStyleBackColor = true;
            this.btnCancel.Click += new EventHandler(this.btnCancel_Click);

            //
            // AdvancedFilterForm
            //
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(484, 582);
            this.Controls.Add(this.btnCancel);
            this.Controls.Add(this.btnReset);
            this.Controls.Add(this.btnApply);
            this.Controls.Add(this.groupBoxRequests);
            this.Controls.Add(this.groupBoxResponseTime);
            this.Controls.Add(this.groupBoxStatusCode);
            this.Controls.Add(this.groupBoxDateRange);
            this.Controls.Add(this.groupBoxMethods);
            this.Controls.Add(this.groupBoxEndpoint);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "AdvancedFilterForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "Advanced Filters";

            this.groupBoxEndpoint.ResumeLayout(false);
            this.groupBoxEndpoint.PerformLayout();
            this.groupBoxMethods.ResumeLayout(false);
            this.groupBoxDateRange.ResumeLayout(false);
            this.groupBoxDateRange.PerformLayout();
            this.groupBoxStatusCode.ResumeLayout(false);
            this.groupBoxStatusCode.PerformLayout();
            this.groupBoxResponseTime.ResumeLayout(false);
            this.groupBoxResponseTime.PerformLayout();
            this.groupBoxRequests.ResumeLayout(false);
            this.groupBoxRequests.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownStatusFrom)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownStatusTo)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMinResponseTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMaxResponseTime)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMinRequests)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.numericUpDownMinClients)).EndInit();
            this.ResumeLayout(false);
        }

        #endregion

        private GroupBox groupBoxEndpoint;
        private TextBox textBoxApiEndpoint;
        private Label labelApiEndpoint;
        private GroupBox groupBoxMethods;
        private FlowLayoutPanel flowLayoutPanelMethods;
        private GroupBox groupBoxDateRange;
        private CheckBox checkBoxDateRange;
        private DateTimePicker dateTimePickerFrom;
        private DateTimePicker dateTimePickerTo;
        private Label labelDateFrom;
        private Label labelDateTo;
        private GroupBox groupBoxStatusCode;
        private ComboBox comboBoxStatusCode;
        private Label labelStatusFrom;
        private Label labelStatusTo;
        private NumericUpDown numericUpDownStatusFrom;
        private NumericUpDown numericUpDownStatusTo;
        private GroupBox groupBoxResponseTime;
        private CheckBox checkBoxResponseTime;
        private NumericUpDown numericUpDownMinResponseTime;
        private NumericUpDown numericUpDownMaxResponseTime;
        private Label labelMinResponseTime;
        private Label labelMaxResponseTime;
        private GroupBox groupBoxRequests;
        private CheckBox checkBoxMinRequests;
        private NumericUpDown numericUpDownMinRequests;
        private CheckBox checkBoxMinClients;
        private NumericUpDown numericUpDownMinClients;
        private Label labelMinRequests;
        private Label labelMinClients;
        private Button btnApply;
        private Button btnReset;
        private Button btnCancel;
    }
}
