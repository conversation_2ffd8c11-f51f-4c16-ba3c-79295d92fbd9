using System;
using System.Collections.Generic;
using System.Linq;

namespace IISLogAnalyzer.Models
{
    /// <summary>
    /// Security analysis data models
    /// </summary>
    public class SecurityAnalysis
    {
        public AuthenticationAnalysis AuthenticationAnalysis { get; set; } = new();
        public List<SuspiciousIP> SuspiciousIPs { get; set; } = new();
        public BotAnalysis BotAnalysis { get; set; } = new();
        public List<AttackPattern> PotentialAttacks { get; set; } = new();
        public SecurityMetrics SecurityMetrics { get; set; } = new();
    }

    /// <summary>
    /// Authentication failure analysis
    /// </summary>
    public class AuthenticationAnalysis
    {
        public int TotalAuthFailures { get; set; }
        public int Unauthorized401Count { get; set; }
        public int Forbidden403Count { get; set; }
        public Dictionary<string, int> FailuresByIP { get; set; } = new();
        public Dictionary<string, int> FailuresByEndpoint { get; set; } = new();
        public Dictionary<DateTime, int> FailuresOverTime { get; set; } = new();
        public List<AuthFailureEvent> RecentFailures { get; set; } = new();
        public double FailureRate { get; set; }
    }

    /// <summary>
    /// Individual authentication failure event
    /// </summary>
    public class AuthFailureEvent
    {
        public DateTime TimeStamp { get; set; }
        public string ClientIP { get; set; } = string.Empty;
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public int StatusCode { get; set; }
        public string UserAgent { get; set; } = string.Empty;
        public string Referer { get; set; } = string.Empty;
    }

    /// <summary>
    /// Suspicious IP address analysis
    /// </summary>
    public class SuspiciousIP
    {
        public string IPAddress { get; set; } = string.Empty;
        public int TotalRequests { get; set; }
        public int ErrorCount { get; set; }
        public double ErrorRate { get; set; }
        public int AuthFailures { get; set; }
        public List<string> TargetedEndpoints { get; set; } = new();
        public List<string> UserAgents { get; set; } = new();
        public DateTime FirstSeen { get; set; }
        public DateTime LastSeen { get; set; }
        public SuspiciousActivityType ActivityType { get; set; }
        public int RiskScore { get; set; } // 1-100
        public string GeographicLocation { get; set; } = string.Empty; // If available
    }

    /// <summary>
    /// Types of suspicious activity
    /// </summary>
    public enum SuspiciousActivityType
    {
        HighErrorRate,
        AuthenticationBruteForce,
        ScanningActivity,
        BotActivity,
        PotentialAttack,
        AbnormalTraffic
    }

    /// <summary>
    /// Bot and crawler analysis
    /// </summary>
    public class BotAnalysis
    {
        public int TotalBotRequests { get; set; }
        public double BotTrafficPercentage { get; set; }
        public Dictionary<string, BotInfo> IdentifiedBots { get; set; } = new();
        public List<string> UnknownBots { get; set; } = new();
        public Dictionary<string, int> BotsByUserAgent { get; set; } = new();
        public List<string> MaliciousBots { get; set; } = new();
    }

    /// <summary>
    /// Information about identified bots
    /// </summary>
    public class BotInfo
    {
        public string Name { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
        public BotType Type { get; set; }
        public int RequestCount { get; set; }
        public List<string> IPAddresses { get; set; } = new();
        public bool IsLegitimate { get; set; }
        public DateTime FirstSeen { get; set; }
        public DateTime LastSeen { get; set; }
    }

    /// <summary>
    /// Types of bots
    /// </summary>
    public enum BotType
    {
        SearchEngine,
        SocialMedia,
        Monitoring,
        Scraper,
        Malicious,
        Unknown
    }

    /// <summary>
    /// Potential attack pattern detection
    /// </summary>
    public class AttackPattern
    {
        public AttackType Type { get; set; }
        public string Description { get; set; } = string.Empty;
        public List<string> AffectedEndpoints { get; set; } = new();
        public List<string> SourceIPs { get; set; } = new();
        public int Severity { get; set; } // 1-10
        public DateTime FirstDetected { get; set; }
        public DateTime LastDetected { get; set; }
        public int OccurrenceCount { get; set; }
        public List<AttackEvent> Events { get; set; } = new();
    }

    /// <summary>
    /// Types of potential attacks
    /// </summary>
    public enum AttackType
    {
        SQLInjection,
        XSSAttempt,
        PathTraversal,
        CommandInjection,
        BruteForce,
        DDoS,
        Scanning,
        Unknown
    }

    /// <summary>
    /// Individual attack event
    /// </summary>
    public class AttackEvent
    {
        public DateTime TimeStamp { get; set; }
        public string ClientIP { get; set; } = string.Empty;
        public string Endpoint { get; set; } = string.Empty;
        public string QueryString { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
        public int StatusCode { get; set; }
        public string DetectionReason { get; set; } = string.Empty;
    }

    /// <summary>
    /// Overall security metrics
    /// </summary>
    public class SecurityMetrics
    {
        public double OverallSecurityScore { get; set; } // 1-100
        public int TotalSecurityEvents { get; set; }
        public int HighRiskEvents { get; set; }
        public int MediumRiskEvents { get; set; }
        public int LowRiskEvents { get; set; }
        public List<string> SecurityRecommendations { get; set; } = new();
        public Dictionary<string, int> ThreatsByType { get; set; } = new();
    }
}
