using System;
using System.Collections.Generic;
using System.Linq;

namespace IISLogAnalyzer.Models
{
    /// <summary>
    /// Represents filtering criteria for IIS log analysis results
    /// </summary>
    public class FilterCriteria
    {
        /// <summary>
        /// Filter by API endpoint (partial match)
        /// </summary>
        public string ApiEndpointFilter { get; set; } = string.Empty;

        /// <summary>
        /// Filter by HTTP methods
        /// </summary>
        public List<string> HttpMethods { get; set; } = new();

        /// <summary>
        /// Filter by date range - start date
        /// </summary>
        public DateTime? DateFrom { get; set; }

        /// <summary>
        /// Filter by date range - end date
        /// </summary>
        public DateTime? DateTo { get; set; }

        /// <summary>
        /// Filter by status code range - minimum
        /// </summary>
        public int? StatusCodeFrom { get; set; }

        /// <summary>
        /// Filter by status code range - maximum
        /// </summary>
        public int? StatusCodeTo { get; set; }

        /// <summary>
        /// Filter by minimum response time (milliseconds)
        /// </summary>
        public int? MinResponseTime { get; set; }

        /// <summary>
        /// Filter by maximum response time (milliseconds)
        /// </summary>
        public int? MaxResponseTime { get; set; }

        /// <summary>
        /// Filter by minimum number of requests
        /// </summary>
        public int? MinimumRequests { get; set; }

        /// <summary>
        /// Filter by minimum number of unique clients
        /// </summary>
        public int? MinimumUniqueClients { get; set; }

        /// <summary>
        /// Check if the filter criteria matches an API analysis result
        /// </summary>
        public bool Matches(ApiAnalysisResult result)
        {
            // Check API endpoint filter
            if (!string.IsNullOrEmpty(ApiEndpointFilter) && 
                !result.ApiEndpoint.Contains(ApiEndpointFilter, StringComparison.OrdinalIgnoreCase))
            {
                return false;
            }

            // Check HTTP method filter
            if (HttpMethods.Any() && !HttpMethods.Contains(result.Method))
            {
                return false;
            }

            // Check date range filter
            if (DateFrom.HasValue && result.LastRequest < DateFrom.Value)
            {
                return false;
            }
            if (DateTo.HasValue && result.FirstRequest > DateTo.Value)
            {
                return false;
            }

            // Check status code filter (based on most common status code)
            if (StatusCodeFrom.HasValue || StatusCodeTo.HasValue)
            {
                var mostCommonStatusCode = result.StatusCodeDistribution
                    .OrderByDescending(kvp => kvp.Value)
                    .FirstOrDefault().Key;

                if (StatusCodeFrom.HasValue && mostCommonStatusCode < StatusCodeFrom.Value)
                {
                    return false;
                }
                if (StatusCodeTo.HasValue && mostCommonStatusCode > StatusCodeTo.Value)
                {
                    return false;
                }
            }

            // Check response time filter
            if (MinResponseTime.HasValue && result.AverageResponseTime < MinResponseTime.Value)
            {
                return false;
            }
            if (MaxResponseTime.HasValue && result.AverageResponseTime > MaxResponseTime.Value)
            {
                return false;
            }

            // Check minimum requests filter
            if (MinimumRequests.HasValue && result.TotalRequests < MinimumRequests.Value)
            {
                return false;
            }

            // Check minimum unique clients filter
            if (MinimumUniqueClients.HasValue && result.UniqueClientCount < MinimumUniqueClients.Value)
            {
                return false;
            }

            return true;
        }

        /// <summary>
        /// Check if any filters are active
        /// </summary>
        public bool HasActiveFilters()
        {
            return !string.IsNullOrEmpty(ApiEndpointFilter) ||
                   (HttpMethods.Any() && HttpMethods.Count < 7) || // Less than all methods
                   DateFrom.HasValue ||
                   DateTo.HasValue ||
                   StatusCodeFrom.HasValue ||
                   StatusCodeTo.HasValue ||
                   MinResponseTime.HasValue ||
                   MaxResponseTime.HasValue ||
                   MinimumRequests.HasValue ||
                   MinimumUniqueClients.HasValue;
        }

        /// <summary>
        /// Get a summary description of active filters
        /// </summary>
        public string GetFilterSummary()
        {
            var filters = new List<string>();

            if (!string.IsNullOrEmpty(ApiEndpointFilter))
                filters.Add($"Endpoint: '{ApiEndpointFilter}'");

            if (HttpMethods.Any() && HttpMethods.Count < 7)
                filters.Add($"Methods: {string.Join(", ", HttpMethods)}");

            if (DateFrom.HasValue || DateTo.HasValue)
            {
                var dateRange = DateFrom?.ToString("yyyy-MM-dd") ?? "Start";
                dateRange += " to " + (DateTo?.ToString("yyyy-MM-dd") ?? "End");
                filters.Add($"Date: {dateRange}");
            }

            if (StatusCodeFrom.HasValue && StatusCodeTo.HasValue)
                filters.Add($"Status: {StatusCodeFrom}-{StatusCodeTo}");

            if (MinResponseTime.HasValue || MaxResponseTime.HasValue)
            {
                var timeRange = (MinResponseTime ?? 0) + "ms";
                timeRange += " to " + (MaxResponseTime?.ToString() ?? "∞") + "ms";
                filters.Add($"Response Time: {timeRange}");
            }

            if (MinimumRequests.HasValue)
                filters.Add($"Min Requests: {MinimumRequests}");

            if (MinimumUniqueClients.HasValue)
                filters.Add($"Min Clients: {MinimumUniqueClients}");

            return filters.Any() ? string.Join(" | ", filters) : "No filters active";
        }

        /// <summary>
        /// Create a copy of the current filter criteria
        /// </summary>
        public FilterCriteria Clone()
        {
            return new FilterCriteria
            {
                ApiEndpointFilter = ApiEndpointFilter,
                HttpMethods = new List<string>(HttpMethods),
                DateFrom = DateFrom,
                DateTo = DateTo,
                StatusCodeFrom = StatusCodeFrom,
                StatusCodeTo = StatusCodeTo,
                MinResponseTime = MinResponseTime,
                MaxResponseTime = MaxResponseTime,
                MinimumRequests = MinimumRequests,
                MinimumUniqueClients = MinimumUniqueClients
            };
        }
    }
}
