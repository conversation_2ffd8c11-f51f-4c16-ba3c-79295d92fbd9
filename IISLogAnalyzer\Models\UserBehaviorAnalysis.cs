using System;
using System.Collections.Generic;
using System.Linq;

namespace IISLogAnalyzer.Models
{
    /// <summary>
    /// User behavior analysis data models
    /// </summary>
    public class UserBehaviorAnalysis
    {
        public List<IPActivityAnalysis> MostActiveIPs { get; set; } = new();
        public UserAgentAnalysis UserAgentAnalysis { get; set; } = new();
        public ReferrerAnalysis ReferrerAnalysis { get; set; } = new();
        public List<UserSession> EstimatedSessions { get; set; } = new();
        public BehaviorMetrics BehaviorMetrics { get; set; } = new();
    }

    /// <summary>
    /// IP address activity analysis
    /// </summary>
    public class IPActivityAnalysis
    {
        public string IPAddress { get; set; } = string.Empty;
        public int TotalRequests { get; set; }
        public int UniqueEndpoints { get; set; }
        public DateTime FirstRequest { get; set; }
        public DateTime LastRequest { get; set; }
        public TimeSpan ActivityDuration { get; set; }
        public double RequestsPerHour { get; set; }
        public List<string> RequestedEndpoints { get; set; } = new();
        public Dictionary<string, int> EndpointFrequency { get; set; } = new();
        public List<string> UserAgents { get; set; } = new();
        public Dictionary<int, int> StatusCodeDistribution { get; set; } = new();
        public UserType UserType { get; set; }
        public string GeographicLocation { get; set; } = string.Empty;
        public List<RequestPattern> RequestPatterns { get; set; } = new();
    }

    /// <summary>
    /// Types of users based on behavior
    /// </summary>
    public enum UserType
    {
        RegularUser,
        PowerUser,
        Bot,
        Scraper,
        Suspicious,
        Developer
    }

    /// <summary>
    /// Request pattern analysis
    /// </summary>
    public class RequestPattern
    {
        public string Pattern { get; set; } = string.Empty;
        public int Frequency { get; set; }
        public TimeSpan AverageInterval { get; set; }
        public bool IsRegular { get; set; }
    }

    /// <summary>
    /// User-Agent analysis
    /// </summary>
    public class UserAgentAnalysis
    {
        public Dictionary<string, int> UserAgentDistribution { get; set; } = new();
        public BrowserAnalysis BrowserAnalysis { get; set; } = new();
        public DeviceAnalysis DeviceAnalysis { get; set; } = new();
        public OperatingSystemAnalysis OSAnalysis { get; set; } = new();
        public List<string> UnknownUserAgents { get; set; } = new();
        public int TotalUniqueUserAgents { get; set; }
    }

    /// <summary>
    /// Browser usage analysis
    /// </summary>
    public class BrowserAnalysis
    {
        public Dictionary<string, int> BrowserDistribution { get; set; } = new();
        public Dictionary<string, int> BrowserVersions { get; set; } = new();
        public string MostPopularBrowser { get; set; } = string.Empty;
        public double ChromePercentage { get; set; }
        public double FirefoxPercentage { get; set; }
        public double SafariPercentage { get; set; }
        public double EdgePercentage { get; set; }
        public double OtherPercentage { get; set; }
    }

    /// <summary>
    /// Device type analysis
    /// </summary>
    public class DeviceAnalysis
    {
        public Dictionary<string, int> DeviceTypes { get; set; } = new();
        public double DesktopPercentage { get; set; }
        public double MobilePercentage { get; set; }
        public double TabletPercentage { get; set; }
        public double BotPercentage { get; set; }
        public Dictionary<string, int> MobileDevices { get; set; } = new();
    }

    /// <summary>
    /// Operating system analysis
    /// </summary>
    public class OperatingSystemAnalysis
    {
        public Dictionary<string, int> OSDistribution { get; set; } = new();
        public double WindowsPercentage { get; set; }
        public double MacOSPercentage { get; set; }
        public double LinuxPercentage { get; set; }
        public double AndroidPercentage { get; set; }
        public double iOSPercentage { get; set; }
        public double OtherPercentage { get; set; }
    }

    /// <summary>
    /// Referrer analysis
    /// </summary>
    public class ReferrerAnalysis
    {
        public Dictionary<string, int> ReferrerDistribution { get; set; } = new();
        public Dictionary<string, int> SearchEngines { get; set; } = new();
        public Dictionary<string, int> SocialMedia { get; set; } = new();
        public Dictionary<string, int> DirectTraffic { get; set; } = new();
        public List<TrafficSource> TopTrafficSources { get; set; } = new();
        public double DirectTrafficPercentage { get; set; }
        public double SearchEnginePercentage { get; set; }
        public double SocialMediaPercentage { get; set; }
        public double OtherReferrerPercentage { get; set; }
    }

    /// <summary>
    /// Traffic source information
    /// </summary>
    public class TrafficSource
    {
        public string Source { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty; // Search, Social, Direct, etc.
        public int RequestCount { get; set; }
        public int UniqueVisitors { get; set; }
        public double Percentage { get; set; }
        public List<string> PopularPages { get; set; } = new();
    }

    /// <summary>
    /// Estimated user session
    /// </summary>
    public class UserSession
    {
        public string IPAddress { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public int PageViews { get; set; }
        public List<string> VisitedPages { get; set; } = new();
        public string EntryPage { get; set; } = string.Empty;
        public string ExitPage { get; set; } = string.Empty;
        public bool BounceSession { get; set; } // Single page visit
        public string Referrer { get; set; } = string.Empty;
    }

    /// <summary>
    /// Overall behavior metrics
    /// </summary>
    public class BehaviorMetrics
    {
        public int TotalUniqueIPs { get; set; }
        public int TotalEstimatedSessions { get; set; }
        public double AverageSessionDuration { get; set; }
        public double AveragePageViewsPerSession { get; set; }
        public double BounceRate { get; set; }
        public int ReturnVisitors { get; set; }
        public int NewVisitors { get; set; }
        public double ReturnVisitorPercentage { get; set; }
        public Dictionary<int, int> SessionDurationDistribution { get; set; } = new();
        public Dictionary<int, int> PageViewsDistribution { get; set; } = new();
    }
}
