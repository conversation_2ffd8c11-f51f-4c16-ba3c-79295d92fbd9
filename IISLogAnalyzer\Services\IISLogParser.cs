using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Services
{
    /// <summary>
    /// Parser for IIS log files in W3C Extended Log Format
    /// </summary>
    public class IISLogParser
    {
        private readonly List<string> _fieldNames = new();
        
        /// <summary>
        /// Event fired when parsing progress updates
        /// </summary>
        public event EventHandler<ProgressEventArgs>? ProgressUpdated;
        
        /// <summary>
        /// Parse IIS log file and return collection of log entries
        /// </summary>
        /// <param name="filePath">Path to the IIS log file</param>
        /// <returns>Collection of parsed log entries</returns>
        public async Task<List<IISLogEntry>> ParseLogFileAsync(string filePath)
        {
            var entries = new List<IISLogEntry>();
            
            if (!File.Exists(filePath))
                throw new FileNotFoundException($"Log file not found: {filePath}");
            
            var lines = await File.ReadAllLinesAsync(filePath);
            var totalLines = lines.Length;
            var processedLines = 0;
            
            foreach (var line in lines)
            {
                processedLines++;
                
                // Report progress every 1000 lines
                if (processedLines % 1000 == 0)
                {
                    var progress = (double)processedLines / totalLines * 100;
                    ProgressUpdated?.Invoke(this, new ProgressEventArgs(progress, $"Processing line {processedLines} of {totalLines}"));
                }
                
                // Skip comments and empty lines
                if (string.IsNullOrWhiteSpace(line) || line.StartsWith("#"))
                {
                    // Check if this is a fields definition line
                    if (line.StartsWith("#Fields:"))
                    {
                        ParseFieldsLine(line);
                    }
                    continue;
                }
                
                // Parse the log entry
                var entry = ParseLogEntry(line);
                if (entry != null)
                {
                    entries.Add(entry);
                }
            }
            
            ProgressUpdated?.Invoke(this, new ProgressEventArgs(100, $"Completed parsing {entries.Count} log entries"));
            return entries;
        }
        
        /// <summary>
        /// Parse multiple IIS log files
        /// </summary>
        /// <param name="filePaths">Collection of file paths</param>
        /// <returns>Combined collection of parsed log entries</returns>
        public async Task<List<IISLogEntry>> ParseMultipleLogFilesAsync(IEnumerable<string> filePaths)
        {
            var allEntries = new List<IISLogEntry>();
            var fileList = filePaths.ToList();
            var currentFile = 0;
            
            foreach (var filePath in fileList)
            {
                currentFile++;
                ProgressUpdated?.Invoke(this, new ProgressEventArgs(
                    (double)currentFile / fileList.Count * 100, 
                    $"Processing file {currentFile} of {fileList.Count}: {Path.GetFileName(filePath)}"));
                
                try
                {
                    var entries = await ParseLogFileAsync(filePath);
                    allEntries.AddRange(entries);
                }
                catch (Exception ex)
                {
                    // Log error but continue with other files
                    ProgressUpdated?.Invoke(this, new ProgressEventArgs(
                        (double)currentFile / fileList.Count * 100, 
                        $"Error parsing {Path.GetFileName(filePath)}: {ex.Message}"));
                }
            }
            
            return allEntries.OrderBy(e => e.DateTime).ToList();
        }
        
        private void ParseFieldsLine(string line)
        {
            // Example: #Fields: date time s-ip cs-method cs-uri-stem cs-uri-query s-port cs-username c-ip cs(User-Agent) cs(Referer) sc-status sc-substatus sc-win32-status sc-bytes cs-bytes time-taken
            var fieldsText = line.Substring("#Fields:".Length).Trim();
            _fieldNames.Clear();
            _fieldNames.AddRange(fieldsText.Split(' ', StringSplitOptions.RemoveEmptyEntries));
        }
        
        private IISLogEntry? ParseLogEntry(string line)
        {
            try
            {
                var fields = line.Split(' ');
                
                if (fields.Length < _fieldNames.Count)
                    return null;
                
                var entry = new IISLogEntry();
                
                for (int i = 0; i < _fieldNames.Count && i < fields.Length; i++)
                {
                    var fieldName = _fieldNames[i];
                    var fieldValue = fields[i];
                    
                    // Skip empty or dash values
                    if (string.IsNullOrEmpty(fieldValue) || fieldValue == "-")
                        continue;
                    
                    switch (fieldName)
                    {
                        case "date":
                            if (DateTime.TryParseExact(fieldValue, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var date))
                                entry.Date = date;
                            break;
                        case "time":
                            if (TimeSpan.TryParseExact(fieldValue, @"hh\:mm\:ss", CultureInfo.InvariantCulture, out var time))
                                entry.Time = time;
                            break;
                        case "s-ip":
                            entry.ServerIP = fieldValue;
                            break;
                        case "cs-method":
                            entry.Method = fieldValue;
                            break;
                        case "cs-uri-stem":
                            entry.UriStem = fieldValue;
                            break;
                        case "cs-uri-query":
                            entry.UriQuery = fieldValue;
                            break;
                        case "s-port":
                            if (int.TryParse(fieldValue, out var port))
                                entry.Port = port;
                            break;
                        case "cs-username":
                            entry.Username = fieldValue;
                            break;
                        case "c-ip":
                            entry.ClientIP = fieldValue;
                            break;
                        case "cs(User-Agent)":
                            entry.UserAgent = fieldValue;
                            break;
                        case "cs(Referer)":
                            entry.Referer = fieldValue;
                            break;
                        case "sc-status":
                            if (int.TryParse(fieldValue, out var status))
                                entry.StatusCode = status;
                            break;
                        case "sc-substatus":
                            if (int.TryParse(fieldValue, out var subStatus))
                                entry.SubStatusCode = subStatus;
                            break;
                        case "sc-win32-status":
                            if (int.TryParse(fieldValue, out var win32Status))
                                entry.Win32StatusCode = win32Status;
                            break;
                        case "sc-bytes":
                            if (long.TryParse(fieldValue, out var bytesSent))
                                entry.BytesSent = bytesSent;
                            break;
                        case "cs-bytes":
                            if (long.TryParse(fieldValue, out var bytesReceived))
                                entry.BytesReceived = bytesReceived;
                            break;
                        case "time-taken":
                            if (int.TryParse(fieldValue, out var timeTaken))
                                entry.TimeTaken = timeTaken;
                            break;
                    }
                }
                
                return entry;
            }
            catch
            {
                return null;
            }
        }
    }
    
    /// <summary>
    /// Event arguments for parsing progress updates
    /// </summary>
    public class ProgressEventArgs : EventArgs
    {
        public double ProgressPercentage { get; }
        public string Message { get; }
        
        public ProgressEventArgs(double progressPercentage, string message)
        {
            ProgressPercentage = progressPercentage;
            Message = message;
        }
    }
}
