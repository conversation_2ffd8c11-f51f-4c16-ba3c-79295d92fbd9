using System;
using System.Collections.Generic;
using System.Linq;

namespace IISLogAnalyzer.Models
{
    /// <summary>
    /// API usage analytics data models
    /// </summary>
    public class ApiUsageAnalytics
    {
        public List<EndpointPopularity> EndpointPopularity { get; set; } = new();
        public RequestSizeAnalysis RequestSizeAnalysis { get; set; } = new();
        public GeographicAnalysis GeographicAnalysis { get; set; } = new();
        public RateLimitingAnalysis RateLimitingAnalysis { get; set; } = new();
        public ApiUsageMetrics UsageMetrics { get; set; } = new();
    }

    /// <summary>
    /// Endpoint popularity ranking
    /// </summary>
    public class EndpointPopularity
    {
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public int TotalRequests { get; set; }
        public int UniqueUsers { get; set; }
        public double PopularityScore { get; set; }
        public double RequestsPerHour { get; set; }
        public Dictionary<DateTime, int> UsageOverTime { get; set; } = new();
        public List<string> TopUserIPs { get; set; } = new();
        public double AverageResponseTime { get; set; }
        public double SuccessRate { get; set; }
        public EndpointCategory Category { get; set; }
        public UsageTrend Trend { get; set; } = new();
    }

    /// <summary>
    /// Endpoint categories
    /// </summary>
    public enum EndpointCategory
    {
        API,
        StaticContent,
        Authentication,
        Admin,
        Public,
        Internal,
        Unknown
    }

    /// <summary>
    /// Usage trend analysis
    /// </summary>
    public class UsageTrend
    {
        public TrendDirection Direction { get; set; }
        public double ChangePercentage { get; set; }
        public Dictionary<DateTime, int> RequestsOverTime { get; set; } = new();
        public bool IsGrowing { get; set; }
        public bool IsStable { get; set; }
        public bool IsDeclining { get; set; }
    }

    /// <summary>
    /// Request size distribution analysis
    /// </summary>
    public class RequestSizeAnalysis
    {
        public Dictionary<string, RequestSizeInfo> SizeByEndpoint { get; set; } = new();
        public Dictionary<string, long> SizeDistribution { get; set; } = new(); // Size ranges
        public long AverageRequestSize { get; set; }
        public long MedianRequestSize { get; set; }
        public long MaxRequestSize { get; set; }
        public long MinRequestSize { get; set; }
        public List<LargeRequest> LargestRequests { get; set; } = new();
        public Dictionary<string, long> SizeByMethod { get; set; } = new();
    }

    /// <summary>
    /// Request size information for specific endpoint
    /// </summary>
    public class RequestSizeInfo
    {
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public long AverageSize { get; set; }
        public long MedianSize { get; set; }
        public long MaxSize { get; set; }
        public long MinSize { get; set; }
        public int RequestCount { get; set; }
        public long TotalSize { get; set; }
        public string FormattedAverageSize { get; set; } = string.Empty;
        public string FormattedTotalSize { get; set; } = string.Empty;
    }

    /// <summary>
    /// Large request details
    /// </summary>
    public class LargeRequest
    {
        public DateTime TimeStamp { get; set; }
        public string ClientIP { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public string Endpoint { get; set; } = string.Empty;
        public long RequestSize { get; set; }
        public long ResponseSize { get; set; }
        public int ResponseTime { get; set; }
        public int StatusCode { get; set; }
        public string UserAgent { get; set; } = string.Empty;
        public string FormattedRequestSize { get; set; } = string.Empty;
        public string FormattedResponseSize { get; set; } = string.Empty;
    }

    /// <summary>
    /// Geographic analysis (if IP geolocation is available)
    /// </summary>
    public class GeographicAnalysis
    {
        public Dictionary<string, CountryInfo> CountryDistribution { get; set; } = new();
        public Dictionary<string, CityInfo> CityDistribution { get; set; } = new();
        public List<string> TopCountries { get; set; } = new();
        public List<string> TopCities { get; set; } = new();
        public bool GeolocationAvailable { get; set; }
        public string DataSource { get; set; } = string.Empty;
    }

    /// <summary>
    /// Country-level traffic information
    /// </summary>
    public class CountryInfo
    {
        public string CountryCode { get; set; } = string.Empty;
        public string CountryName { get; set; } = string.Empty;
        public int RequestCount { get; set; }
        public int UniqueIPs { get; set; }
        public double Percentage { get; set; }
        public List<string> PopularEndpoints { get; set; } = new();
        public double AverageResponseTime { get; set; }
    }

    /// <summary>
    /// City-level traffic information
    /// </summary>
    public class CityInfo
    {
        public string CityName { get; set; } = string.Empty;
        public string CountryCode { get; set; } = string.Empty;
        public int RequestCount { get; set; }
        public int UniqueIPs { get; set; }
        public double Percentage { get; set; }
        public List<string> PopularEndpoints { get; set; } = new();
    }

    /// <summary>
    /// Rate limiting violations detection
    /// </summary>
    public class RateLimitingAnalysis
    {
        public List<RateLimitViolation> Violations { get; set; } = new();
        public Dictionary<string, int> ViolationsByIP { get; set; } = new();
        public Dictionary<string, int> ViolationsByEndpoint { get; set; } = new();
        public int TotalViolations { get; set; }
        public List<string> FrequentViolators { get; set; } = new();
        public Dictionary<DateTime, int> ViolationsOverTime { get; set; } = new();
        public RateLimitingMetrics Metrics { get; set; } = new();
    }

    /// <summary>
    /// Rate limit violation event
    /// </summary>
    public class RateLimitViolation
    {
        public DateTime TimeStamp { get; set; }
        public string ClientIP { get; set; } = string.Empty;
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public int RequestsInWindow { get; set; }
        public TimeSpan TimeWindow { get; set; }
        public int AllowedLimit { get; set; }
        public ViolationType Type { get; set; }
        public int Severity { get; set; } // 1-10
    }

    /// <summary>
    /// Types of rate limit violations
    /// </summary>
    public enum ViolationType
    {
        BurstTraffic,
        SustainedHigh,
        Suspicious,
        Automated
    }

    /// <summary>
    /// Rate limiting metrics
    /// </summary>
    public class RateLimitingMetrics
    {
        public double AverageRequestsPerMinute { get; set; }
        public double PeakRequestsPerMinute { get; set; }
        public Dictionary<string, double> RequestRateByIP { get; set; } = new();
        public Dictionary<string, double> RequestRateByEndpoint { get; set; } = new();
        public List<string> RecommendedRateLimits { get; set; } = new();
    }

    /// <summary>
    /// Overall API usage metrics
    /// </summary>
    public class ApiUsageMetrics
    {
        public int TotalApiCalls { get; set; }
        public int UniqueEndpoints { get; set; }
        public int UniqueUsers { get; set; }
        public double AverageCallsPerUser { get; set; }
        public double AverageCallsPerEndpoint { get; set; }
        public Dictionary<string, int> MethodDistribution { get; set; } = new();
        public Dictionary<string, double> EndpointUtilization { get; set; } = new();
        public List<string> UnderutilizedEndpoints { get; set; } = new();
        public List<string> OverutilizedEndpoints { get; set; } = new();
        public ApiHealthScore HealthScore { get; set; } = new();
    }

    /// <summary>
    /// API health scoring
    /// </summary>
    public class ApiHealthScore
    {
        public double OverallScore { get; set; } // 1-100
        public double PerformanceScore { get; set; }
        public double ReliabilityScore { get; set; }
        public double SecurityScore { get; set; }
        public double UsabilityScore { get; set; }
        public List<string> Recommendations { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }
}
