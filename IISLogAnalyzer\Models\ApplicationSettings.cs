using System;
using System.IO;
using Newtonsoft.Json;

namespace IISLogAnalyzer.Models
{
    /// <summary>
    /// Application settings and configuration
    /// </summary>
    public class ApplicationSettings
    {
        /// <summary>
        /// Default number of top APIs to display
        /// </summary>
        public int DefaultTopApiCount { get; set; } = 10;

        /// <summary>
        /// Default response time threshold for slow APIs (milliseconds)
        /// </summary>
        public int SlowApiThreshold { get; set; } = 1000;

        /// <summary>
        /// Default error rate threshold for problematic APIs (percentage)
        /// </summary>
        public double ErrorRateThreshold { get; set; } = 5.0;

        /// <summary>
        /// Auto-refresh interval for real-time monitoring (seconds, 0 = disabled)
        /// </summary>
        public int AutoRefreshInterval { get; set; } = 0;

        /// <summary>
        /// Maximum number of log entries to process (0 = unlimited)
        /// </summary>
        public int MaxLogEntries { get; set; } = 0;

        /// <summary>
        /// Default export format
        /// </summary>
        public string DefaultExportFormat { get; set; } = "CSV";

        /// <summary>
        /// Remember last opened directory
        /// </summary>
        public bool RememberLastDirectory { get; set; } = true;

        /// <summary>
        /// Last opened directory path
        /// </summary>
        public string LastDirectory { get; set; } = string.Empty;

        /// <summary>
        /// Window state and position settings
        /// </summary>
        public WindowSettings WindowSettings { get; set; } = new();

        /// <summary>
        /// Color coding settings
        /// </summary>
        public ColorSettings ColorSettings { get; set; } = new();

        /// <summary>
        /// Performance settings
        /// </summary>
        public PerformanceSettings PerformanceSettings { get; set; } = new();

        /// <summary>
        /// Load settings from file
        /// </summary>
        public static ApplicationSettings Load()
        {
            try
            {
                var settingsPath = GetSettingsPath();
                if (File.Exists(settingsPath))
                {
                    var json = File.ReadAllText(settingsPath);
                    return JsonConvert.DeserializeObject<ApplicationSettings>(json) ?? new ApplicationSettings();
                }
            }
            catch
            {
                // If loading fails, return default settings
            }
            
            return new ApplicationSettings();
        }

        /// <summary>
        /// Save settings to file
        /// </summary>
        public void Save()
        {
            try
            {
                var settingsPath = GetSettingsPath();
                var directory = Path.GetDirectoryName(settingsPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonConvert.SerializeObject(this, Formatting.Indented);
                File.WriteAllText(settingsPath, json);
            }
            catch
            {
                // Silently fail if saving settings fails
            }
        }

        /// <summary>
        /// Get the settings file path
        /// </summary>
        private static string GetSettingsPath()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            return Path.Combine(appDataPath, "IISLogAnalyzer", "settings.json");
        }

        /// <summary>
        /// Reset to default settings
        /// </summary>
        public void ResetToDefaults()
        {
            DefaultTopApiCount = 10;
            SlowApiThreshold = 1000;
            ErrorRateThreshold = 5.0;
            AutoRefreshInterval = 0;
            MaxLogEntries = 0;
            DefaultExportFormat = "CSV";
            RememberLastDirectory = true;
            LastDirectory = string.Empty;
            WindowSettings = new WindowSettings();
            ColorSettings = new ColorSettings();
            PerformanceSettings = new PerformanceSettings();
        }
    }

    /// <summary>
    /// Window state and position settings
    /// </summary>
    public class WindowSettings
    {
        public int Width { get; set; } = 1200;
        public int Height { get; set; } = 800;
        public int X { get; set; } = -1; // -1 means center
        public int Y { get; set; } = -1; // -1 means center
        public bool Maximized { get; set; } = false;
    }

    /// <summary>
    /// Color coding settings for different metrics
    /// </summary>
    public class ColorSettings
    {
        public string SuccessColor { get; set; } = "#90EE90"; // LightGreen
        public string WarningColor { get; set; } = "#FFFFE0"; // LightYellow
        public string ErrorColor { get; set; } = "#F0A0A0"; // LightCoral
        public string InfoColor { get; set; } = "#ADD8E6"; // LightBlue
        
        public double SuccessThreshold { get; set; } = 95.0; // Success rate >= 95%
        public double WarningThreshold { get; set; } = 90.0; // Success rate >= 90%
    }

    /// <summary>
    /// Performance and processing settings
    /// </summary>
    public class PerformanceSettings
    {
        public int BatchSize { get; set; } = 1000; // Process logs in batches
        public bool EnableProgressReporting { get; set; } = true;
        public int ProgressUpdateInterval { get; set; } = 1000; // Update progress every N lines
        public bool EnableMemoryOptimization { get; set; } = true;
        public int MaxCacheSize { get; set; } = 10000; // Maximum cached analysis results
    }
}
