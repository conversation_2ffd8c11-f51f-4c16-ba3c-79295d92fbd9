using IISLogAnalyzer.Services;
using IISLogAnalyzer.Utilities;

namespace IISLogAnalyzer
{
    static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            var logger = LoggingService.Instance;

            try
            {
                // Set up global exception handling
                ErrorHandler.SetupGlobalExceptionHandling();

                // Log application startup
                logger.LogApplicationStart();

                // Clean up old log files
                logger.CleanupOldLogs();

                // To customize application configuration such as set high DPI settings or default font,
                // see https://aka.ms/applicationconfiguration.
                ApplicationConfiguration.Initialize();

                logger.LogInfo("Starting main application form");
                Application.Run(new MainForm());

                logger.LogApplicationShutdown();
            }
            catch (Exception ex)
            {
                logger.LogFatal("Fatal error during application startup", ex);

                MessageBox.Show(
                    $"A fatal error occurred during application startup:\n\n{ex.Message}\n\nPlease check the log files for more details.",
                    "Fatal Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }
    }
}