using System;
using System.Collections.Generic;
using System.Linq;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Services
{
    /// <summary>
    /// Service for analyzing error patterns and trends in IIS log data
    /// </summary>
    public class ErrorAnalysisService
    {
        private readonly Dictionary<int, ErrorCodeInfo> _errorCodeDescriptions;

        public ErrorAnalysisService()
        {
            _errorCodeDescriptions = InitializeErrorCodeDescriptions();
        }

        /// <summary>
        /// Perform comprehensive error analysis
        /// </summary>
        public ErrorAnalysis AnalyzeErrors(List<IISLogEntry> logEntries)
        {
            if (!logEntries.Any())
                return new ErrorAnalysis();

            var errorEntries = logEntries.Where(e => !e.IsSuccessful).ToList();

            var analysis = new ErrorAnalysis
            {
                ErrorCodeAnalysis = AnalyzeErrorCodes(errorEntries, logEntries.Count),
                ErrorTrends = AnalyzeErrorTrends(errorEntries, logEntries),
                ProblematicEndpoints = AnalyzeProblematicEndpoints(logEntries),
                ClientVsServerErrors = AnalyzeClientVsServerErrors(errorEntries),
                RecentErrors = GetRecentErrors(errorEntries)
            };

            return analysis;
        }

        /// <summary>
        /// Analyze error code distribution and details
        /// </summary>
        private ErrorCodeAnalysis AnalyzeErrorCodes(List<IISLogEntry> errorEntries, int totalRequests)
        {
            var analysis = new ErrorCodeAnalysis
            {
                TotalErrors = errorEntries.Count,
                OverallErrorRate = totalRequests > 0 ? (double)errorEntries.Count / totalRequests * 100 : 0
            };

            // Group errors by status code
            var errorGroups = errorEntries.GroupBy(e => e.StatusCode);
            
            foreach (var group in errorGroups)
            {
                var statusCode = group.Key;
                var count = group.Count();
                var percentage = (double)count / errorEntries.Count * 100;

                var errorInfo = _errorCodeDescriptions.ContainsKey(statusCode) 
                    ? _errorCodeDescriptions[statusCode] 
                    : new ErrorCodeInfo
                    {
                        StatusCode = statusCode,
                        Description = "Unknown Error",
                        Category = statusCode >= 400 && statusCode < 500 ? "Client Error" : "Server Error",
                        Severity = statusCode >= 500 ? ErrorSeverity.High : ErrorSeverity.Medium
                    };

                errorInfo.Count = count;
                errorInfo.Percentage = percentage;
                errorInfo.AffectedEndpoints = group.Select(e => e.ApiEndpoint).Distinct().ToList();

                analysis.ErrorCodes[statusCode] = errorInfo;
            }

            // Most common errors
            analysis.MostCommonErrors = analysis.ErrorCodes.Values
                .OrderByDescending(e => e.Count)
                .Take(10)
                .ToList();

            // Errors by endpoint
            analysis.ErrorsByEndpoint = errorEntries
                .GroupBy(e => e.ApiEndpoint)
                .ToDictionary(
                    g => g.Key,
                    g => g.GroupBy(e => e.StatusCode).ToDictionary(sg => sg.Key, sg => sg.Count())
                );

            return analysis;
        }

        /// <summary>
        /// Analyze error trends over time
        /// </summary>
        private ErrorTrendAnalysis AnalyzeErrorTrends(List<IISLogEntry> errorEntries, List<IISLogEntry> allEntries)
        {
            var analysis = new ErrorTrendAnalysis();

            // Hourly error counts
            var hourlyErrors = errorEntries
                .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, 0, 0))
                .ToDictionary(g => g.Key, g => g.Count());

            analysis.HourlyErrorCounts = hourlyErrors;

            // Hourly error rates
            var hourlyTotal = allEntries
                .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, 0, 0))
                .ToDictionary(g => g.Key, g => g.Count());

            analysis.HourlyErrorRates = hourlyErrors.ToDictionary(
                kvp => kvp.Key,
                kvp => hourlyTotal.ContainsKey(kvp.Key) ? (double)kvp.Value / hourlyTotal[kvp.Key] * 100 : 0
            );

            // Error code trends
            analysis.ErrorCodeTrends = errorEntries
                .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, 0, 0))
                .ToDictionary(
                    g => g.Key,
                    g => g.GroupBy(e => e.StatusCode).ToDictionary(sg => sg.Key, sg => sg.Count())
                );

            // Detect error spikes
            analysis.ErrorSpikes = DetectErrorSpikes(hourlyErrors, hourlyTotal);

            // Overall trend
            analysis.OverallTrend = CalculateOverallTrend(hourlyErrors);

            return analysis;
        }

        /// <summary>
        /// Analyze problematic endpoints
        /// </summary>
        private List<ProblematicEndpoint> AnalyzeProblematicEndpoints(List<IISLogEntry> logEntries)
        {
            var endpointGroups = logEntries.GroupBy(e => new { e.Method, e.ApiEndpoint });
            var problematicEndpoints = new List<ProblematicEndpoint>();

            foreach (var group in endpointGroups.Where(g => g.Count() >= 10)) // Minimum threshold
            {
                var entries = group.ToList();
                var errorCount = entries.Count(e => !e.IsSuccessful);
                var errorRate = (double)errorCount / entries.Count * 100;

                if (errorRate >= 5) // Only endpoints with significant error rates
                {
                    var endpoint = new ProblematicEndpoint
                    {
                        Endpoint = group.Key.ApiEndpoint,
                        Method = group.Key.Method,
                        TotalRequests = entries.Count,
                        ErrorCount = errorCount,
                        ErrorRate = errorRate,
                        ErrorCodeDistribution = entries.Where(e => !e.IsSuccessful)
                            .GroupBy(e => e.StatusCode)
                            .ToDictionary(g => g.Key, g => g.Count()),
                        RecentErrors = entries.Where(e => !e.IsSuccessful)
                            .OrderByDescending(e => e.DateTime)
                            .Take(10)
                            .Select(e => CreateErrorEvent(e))
                            .ToList(),
                        Trend = AnalyzeEndpointErrorTrend(entries),
                        PriorityScore = CalculateEndpointPriorityScore(errorRate, errorCount, entries.Count)
                    };

                    endpoint.CommonErrorPatterns = IdentifyErrorPatterns(entries.Where(e => !e.IsSuccessful).ToList());
                    endpoint.Recommendation = GenerateEndpointRecommendation(endpoint);

                    problematicEndpoints.Add(endpoint);
                }
            }

            return problematicEndpoints.OrderByDescending(e => e.PriorityScore).Take(20).ToList();
        }

        /// <summary>
        /// Analyze client vs server errors
        /// </summary>
        private ClientVsServerErrorAnalysis AnalyzeClientVsServerErrors(List<IISLogEntry> errorEntries)
        {
            var clientErrors = errorEntries.Where(e => e.IsClientError).ToList();
            var serverErrors = errorEntries.Where(e => e.IsServerError).ToList();

            var analysis = new ClientVsServerErrorAnalysis
            {
                ClientErrorPercentage = errorEntries.Any() ? (double)clientErrors.Count / errorEntries.Count * 100 : 0,
                ServerErrorPercentage = errorEntries.Any() ? (double)serverErrors.Count / errorEntries.Count * 100 : 0
            };

            // Client error analysis
            analysis.ClientErrors = new ClientErrorAnalysis
            {
                TotalClientErrors = clientErrors.Count,
                NotFoundErrors = clientErrors.Count(e => e.StatusCode == 404),
                UnauthorizedErrors = clientErrors.Count(e => e.StatusCode == 401),
                ForbiddenErrors = clientErrors.Count(e => e.StatusCode == 403),
                BadRequestErrors = clientErrors.Count(e => e.StatusCode == 400),
                ClientErrorDistribution = clientErrors.GroupBy(e => e.StatusCode).ToDictionary(g => g.Key, g => g.Count()),
                MostRequestedMissingPages = clientErrors.Where(e => e.StatusCode == 404)
                    .GroupBy(e => e.ApiEndpoint)
                    .OrderByDescending(g => g.Count())
                    .Take(10)
                    .Select(g => g.Key)
                    .ToList(),
                CommonBadRequestPatterns = IdentifyBadRequestPatterns(clientErrors.Where(e => e.StatusCode == 400).ToList())
            };

            analysis.ClientErrors.OtherClientErrors = analysis.ClientErrors.TotalClientErrors -
                analysis.ClientErrors.NotFoundErrors - analysis.ClientErrors.UnauthorizedErrors -
                analysis.ClientErrors.ForbiddenErrors - analysis.ClientErrors.BadRequestErrors;

            // Server error analysis
            analysis.ServerErrors = new ServerErrorAnalysis
            {
                TotalServerErrors = serverErrors.Count,
                InternalServerErrors = serverErrors.Count(e => e.StatusCode == 500),
                BadGatewayErrors = serverErrors.Count(e => e.StatusCode == 502),
                ServiceUnavailableErrors = serverErrors.Count(e => e.StatusCode == 503),
                GatewayTimeoutErrors = serverErrors.Count(e => e.StatusCode == 504),
                ServerErrorDistribution = serverErrors.GroupBy(e => e.StatusCode).ToDictionary(g => g.Key, g => g.Count()),
                MostProblematicEndpoints = serverErrors.GroupBy(e => e.ApiEndpoint)
                    .OrderByDescending(g => g.Count())
                    .Take(10)
                    .Select(g => g.Key)
                    .ToList(),
                ErrorSpikeTimes = DetectServerErrorSpikes(serverErrors)
            };

            analysis.ServerErrors.OtherServerErrors = analysis.ServerErrors.TotalServerErrors -
                analysis.ServerErrors.InternalServerErrors - analysis.ServerErrors.BadGatewayErrors -
                analysis.ServerErrors.ServiceUnavailableErrors - analysis.ServerErrors.GatewayTimeoutErrors;

            // Trends
            analysis.ClientErrorTrend = clientErrors
                .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, 0, 0))
                .ToDictionary(g => g.Key, g => g.Count());

            analysis.ServerErrorTrend = serverErrors
                .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, 0, 0))
                .ToDictionary(g => g.Key, g => g.Count());

            return analysis;
        }

        /// <summary>
        /// Get recent error events
        /// </summary>
        private List<ErrorEvent> GetRecentErrors(List<IISLogEntry> errorEntries)
        {
            return errorEntries
                .OrderByDescending(e => e.DateTime)
                .Take(100)
                .Select(e => CreateErrorEvent(e))
                .ToList();
        }

        /// <summary>
        /// Create error event from log entry
        /// </summary>
        private ErrorEvent CreateErrorEvent(IISLogEntry entry)
        {
            var errorEvent = new ErrorEvent
            {
                TimeStamp = entry.DateTime,
                ClientIP = entry.ClientIP,
                Method = entry.Method,
                Endpoint = entry.ApiEndpoint,
                QueryString = entry.UriQuery,
                StatusCode = entry.StatusCode,
                SubStatusCode = entry.SubStatusCode,
                Win32StatusCode = entry.Win32StatusCode,
                UserAgent = entry.UserAgent,
                Referer = entry.Referer,
                ResponseTime = entry.TimeTaken,
                BytesSent = entry.BytesSent,
                ErrorCategory = entry.IsClientError ? "Client Error" : "Server Error"
            };

            errorEvent.PossibleCause = DeterminePossibleCause(entry);

            return errorEvent;
        }

        /// <summary>
        /// Initialize error code descriptions
        /// </summary>
        private Dictionary<int, ErrorCodeInfo> InitializeErrorCodeDescriptions()
        {
            return new Dictionary<int, ErrorCodeInfo>
            {
                [400] = new ErrorCodeInfo { StatusCode = 400, Description = "Bad Request", Category = "Client Error", Severity = ErrorSeverity.Medium, CommonCauses = new List<string> { "Invalid request syntax", "Malformed request" }, Recommendation = "Validate request format and parameters" },
                [401] = new ErrorCodeInfo { StatusCode = 401, Description = "Unauthorized", Category = "Client Error", Severity = ErrorSeverity.High, CommonCauses = new List<string> { "Missing authentication", "Invalid credentials" }, Recommendation = "Implement proper authentication and review access controls" },
                [403] = new ErrorCodeInfo { StatusCode = 403, Description = "Forbidden", Category = "Client Error", Severity = ErrorSeverity.High, CommonCauses = new List<string> { "Insufficient permissions", "Access denied" }, Recommendation = "Review authorization rules and user permissions" },
                [404] = new ErrorCodeInfo { StatusCode = 404, Description = "Not Found", Category = "Client Error", Severity = ErrorSeverity.Low, CommonCauses = new List<string> { "Resource doesn't exist", "Incorrect URL" }, Recommendation = "Check for broken links and implement proper URL routing" },
                [405] = new ErrorCodeInfo { StatusCode = 405, Description = "Method Not Allowed", Category = "Client Error", Severity = ErrorSeverity.Medium, CommonCauses = new List<string> { "HTTP method not supported" }, Recommendation = "Verify supported HTTP methods for endpoints" },
                [408] = new ErrorCodeInfo { StatusCode = 408, Description = "Request Timeout", Category = "Client Error", Severity = ErrorSeverity.Medium, CommonCauses = new List<string> { "Client took too long to send request" }, Recommendation = "Review timeout settings and client behavior" },
                [409] = new ErrorCodeInfo { StatusCode = 409, Description = "Conflict", Category = "Client Error", Severity = ErrorSeverity.Medium, CommonCauses = new List<string> { "Resource conflict", "Concurrent modification" }, Recommendation = "Implement proper conflict resolution" },
                [410] = new ErrorCodeInfo { StatusCode = 410, Description = "Gone", Category = "Client Error", Severity = ErrorSeverity.Low, CommonCauses = new List<string> { "Resource permanently removed" }, Recommendation = "Update references to removed resources" },
                [413] = new ErrorCodeInfo { StatusCode = 413, Description = "Payload Too Large", Category = "Client Error", Severity = ErrorSeverity.Medium, CommonCauses = new List<string> { "Request body too large" }, Recommendation = "Review request size limits" },
                [414] = new ErrorCodeInfo { StatusCode = 414, Description = "URI Too Long", Category = "Client Error", Severity = ErrorSeverity.Medium, CommonCauses = new List<string> { "URL exceeds length limit" }, Recommendation = "Use POST for large parameter sets" },
                [415] = new ErrorCodeInfo { StatusCode = 415, Description = "Unsupported Media Type", Category = "Client Error", Severity = ErrorSeverity.Medium, CommonCauses = new List<string> { "Invalid content type" }, Recommendation = "Verify supported content types" },
                [422] = new ErrorCodeInfo { StatusCode = 422, Description = "Unprocessable Entity", Category = "Client Error", Severity = ErrorSeverity.Medium, CommonCauses = new List<string> { "Validation errors" }, Recommendation = "Improve input validation and error messages" },
                [429] = new ErrorCodeInfo { StatusCode = 429, Description = "Too Many Requests", Category = "Client Error", Severity = ErrorSeverity.Medium, CommonCauses = new List<string> { "Rate limit exceeded" }, Recommendation = "Implement proper rate limiting and client throttling" },
                [500] = new ErrorCodeInfo { StatusCode = 500, Description = "Internal Server Error", Category = "Server Error", Severity = ErrorSeverity.Critical, CommonCauses = new List<string> { "Application error", "Unhandled exception" }, Recommendation = "Review application logs and fix underlying issues" },
                [501] = new ErrorCodeInfo { StatusCode = 501, Description = "Not Implemented", Category = "Server Error", Severity = ErrorSeverity.Medium, CommonCauses = new List<string> { "Feature not implemented" }, Recommendation = "Implement missing functionality" },
                [502] = new ErrorCodeInfo { StatusCode = 502, Description = "Bad Gateway", Category = "Server Error", Severity = ErrorSeverity.High, CommonCauses = new List<string> { "Upstream server error", "Gateway configuration issue" }, Recommendation = "Check upstream services and gateway configuration" },
                [503] = new ErrorCodeInfo { StatusCode = 503, Description = "Service Unavailable", Category = "Server Error", Severity = ErrorSeverity.High, CommonCauses = new List<string> { "Server overloaded", "Maintenance mode" }, Recommendation = "Scale resources or implement load balancing" },
                [504] = new ErrorCodeInfo { StatusCode = 504, Description = "Gateway Timeout", Category = "Server Error", Severity = ErrorSeverity.High, CommonCauses = new List<string> { "Upstream timeout", "Slow backend response" }, Recommendation = "Optimize backend performance and timeout settings" },
                [505] = new ErrorCodeInfo { StatusCode = 505, Description = "HTTP Version Not Supported", Category = "Server Error", Severity = ErrorSeverity.Low, CommonCauses = new List<string> { "Unsupported HTTP version" }, Recommendation = "Update server to support required HTTP versions" }
            };
        }

        /// <summary>
        /// Detect error spikes in hourly data
        /// </summary>
        private List<ErrorSpike> DetectErrorSpikes(Dictionary<DateTime, int> hourlyErrors, Dictionary<DateTime, int> hourlyTotal)
        {
            var spikes = new List<ErrorSpike>();

            if (!hourlyErrors.Any()) return spikes;

            var averageErrors = hourlyErrors.Values.Average();
            var threshold = averageErrors * 3; // 3x average is considered a spike

            foreach (var hour in hourlyErrors.Where(kvp => kvp.Value > threshold))
            {
                var totalRequests = hourlyTotal.GetValueOrDefault(hour.Key, 1);
                var errorRate = (double)hour.Value / totalRequests * 100;

                spikes.Add(new ErrorSpike
                {
                    TimeStamp = hour.Key,
                    ErrorCount = hour.Value,
                    ErrorRate = errorRate,
                    SpikeMultiplier = hour.Value / averageErrors,
                    PossibleCause = DeterminePossibleSpikeCause(hour.Value, errorRate)
                });
            }

            return spikes.OrderByDescending(s => s.SpikeMultiplier).ToList();
        }

        /// <summary>
        /// Calculate overall error trend
        /// </summary>
        private TrendDirection CalculateOverallTrend(Dictionary<DateTime, int> hourlyErrors)
        {
            if (hourlyErrors.Count < 2) return TrendDirection.Stable;

            var sortedHours = hourlyErrors.OrderBy(kvp => kvp.Key).ToList();
            var firstHalf = sortedHours.Take(sortedHours.Count / 2).Sum(kvp => kvp.Value);
            var secondHalf = sortedHours.Skip(sortedHours.Count / 2).Sum(kvp => kvp.Value);

            var changePercentage = firstHalf > 0 ? (double)(secondHalf - firstHalf) / firstHalf * 100 : 0;

            if (changePercentage > 20) return TrendDirection.Worsening;
            if (changePercentage < -20) return TrendDirection.Improving;
            return TrendDirection.Stable;
        }

        /// <summary>
        /// Analyze error trend for specific endpoint
        /// </summary>
        private ErrorTrend AnalyzeEndpointErrorTrend(List<IISLogEntry> entries)
        {
            var errorEntries = entries.Where(e => !e.IsSuccessful).ToList();
            var hourlyErrors = errorEntries
                .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, 0, 0))
                .ToDictionary(g => g.Key, g => g.Count());

            var hourlyTotal = entries
                .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, 0, 0))
                .ToDictionary(g => g.Key, g => g.Count());

            var errorRates = hourlyErrors.ToDictionary(
                kvp => kvp.Key,
                kvp => hourlyTotal.ContainsKey(kvp.Key) ? (double)kvp.Value / hourlyTotal[kvp.Key] * 100 : 0
            );

            var trend = new ErrorTrend
            {
                Direction = CalculateOverallTrend(hourlyErrors),
                ErrorRateOverTime = errorRates
            };

            if (errorRates.Count >= 2)
            {
                var sortedRates = errorRates.OrderBy(kvp => kvp.Key).ToList();
                var firstRate = sortedRates.Take(sortedRates.Count / 2).Average(kvp => kvp.Value);
                var lastRate = sortedRates.Skip(sortedRates.Count / 2).Average(kvp => kvp.Value);

                trend.ChangePercentage = firstRate > 0 ? (lastRate - firstRate) / firstRate * 100 : 0;
            }

            return trend;
        }

        /// <summary>
        /// Calculate priority score for problematic endpoint
        /// </summary>
        private int CalculateEndpointPriorityScore(double errorRate, int errorCount, int totalRequests)
        {
            int score = 0;

            // Error rate contribution (0-40 points)
            score += (int)(errorRate * 0.4);

            // Error count contribution (0-30 points)
            if (errorCount > 1000) score += 30;
            else if (errorCount > 500) score += 20;
            else if (errorCount > 100) score += 10;
            else if (errorCount > 50) score += 5;

            // Traffic volume contribution (0-20 points)
            if (totalRequests > 10000) score += 20;
            else if (totalRequests > 5000) score += 15;
            else if (totalRequests > 1000) score += 10;
            else if (totalRequests > 500) score += 5;

            // Recent activity boost (0-10 points)
            score += 10; // Assume recent activity for now

            return Math.Min(100, score);
        }

        /// <summary>
        /// Identify common error patterns
        /// </summary>
        private List<string> IdentifyErrorPatterns(List<IISLogEntry> errorEntries)
        {
            var patterns = new List<string>();

            // Check for common patterns
            var statusCodes = errorEntries.GroupBy(e => e.StatusCode).ToDictionary(g => g.Key, g => g.Count());
            var dominantStatus = statusCodes.OrderByDescending(kvp => kvp.Value).FirstOrDefault();

            if (dominantStatus.Value > errorEntries.Count * 0.8)
            {
                patterns.Add($"Predominantly {dominantStatus.Key} errors");
            }

            // Check for time-based patterns
            var hourlyDistribution = errorEntries.GroupBy(e => e.DateTime.Hour).ToDictionary(g => g.Key, g => g.Count());
            var peakHour = hourlyDistribution.OrderByDescending(kvp => kvp.Value).FirstOrDefault();

            if (peakHour.Value > errorEntries.Count * 0.3)
            {
                patterns.Add($"Peak errors at hour {peakHour.Key}");
            }

            // Check for IP-based patterns
            var ipDistribution = errorEntries.GroupBy(e => e.ClientIP).ToDictionary(g => g.Key, g => g.Count());
            var topIP = ipDistribution.OrderByDescending(kvp => kvp.Value).FirstOrDefault();

            if (topIP.Value > errorEntries.Count * 0.5)
            {
                patterns.Add($"High error rate from IP {topIP.Key}");
            }

            return patterns;
        }

        /// <summary>
        /// Generate recommendation for problematic endpoint
        /// </summary>
        private string GenerateEndpointRecommendation(ProblematicEndpoint endpoint)
        {
            var recommendations = new List<string>();

            if (endpoint.ErrorRate > 50)
                recommendations.Add("Critical: Immediate investigation required");
            else if (endpoint.ErrorRate > 20)
                recommendations.Add("High priority: Review and fix within 24 hours");
            else if (endpoint.ErrorRate > 10)
                recommendations.Add("Medium priority: Schedule for review");

            var dominantError = endpoint.ErrorCodeDistribution.OrderByDescending(kvp => kvp.Value).FirstOrDefault();
            if (_errorCodeDescriptions.ContainsKey(dominantError.Key))
            {
                recommendations.Add(_errorCodeDescriptions[dominantError.Key].Recommendation);
            }

            return string.Join("; ", recommendations);
        }

        /// <summary>
        /// Identify bad request patterns
        /// </summary>
        private List<string> IdentifyBadRequestPatterns(List<IISLogEntry> badRequestEntries)
        {
            var patterns = new List<string>();

            // Common bad request patterns
            var queryPatterns = badRequestEntries
                .Where(e => !string.IsNullOrEmpty(e.UriQuery))
                .GroupBy(e => e.UriQuery)
                .Where(g => g.Count() > 5)
                .OrderByDescending(g => g.Count())
                .Take(5)
                .Select(g => g.Key)
                .ToList();

            patterns.AddRange(queryPatterns);

            return patterns;
        }

        /// <summary>
        /// Detect server error spikes
        /// </summary>
        private List<DateTime> DetectServerErrorSpikes(List<IISLogEntry> serverErrors)
        {
            var hourlyErrors = serverErrors
                .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, 0, 0))
                .ToDictionary(g => g.Key, g => g.Count());

            if (!hourlyErrors.Any()) return new List<DateTime>();

            var average = hourlyErrors.Values.Average();
            var threshold = average * 2;

            return hourlyErrors
                .Where(kvp => kvp.Value > threshold)
                .Select(kvp => kvp.Key)
                .OrderByDescending(dt => hourlyErrors[dt])
                .Take(10)
                .ToList();
        }

        /// <summary>
        /// Determine possible cause for error
        /// </summary>
        private string DeterminePossibleCause(IISLogEntry entry)
        {
            switch (entry.StatusCode)
            {
                case 400: return "Invalid request format or parameters";
                case 401: return "Authentication required or failed";
                case 403: return "Access denied or insufficient permissions";
                case 404: return "Resource not found or incorrect URL";
                case 405: return "HTTP method not allowed for this endpoint";
                case 408: return "Request timeout";
                case 413: return "Request payload too large";
                case 429: return "Rate limit exceeded";
                case 500: return "Internal server error or application exception";
                case 502: return "Bad gateway or upstream server error";
                case 503: return "Service unavailable or server overloaded";
                case 504: return "Gateway timeout or slow backend response";
                default: return "Unknown error condition";
            }
        }

        /// <summary>
        /// Determine possible cause for error spike
        /// </summary>
        private string DeterminePossibleSpikeCause(int errorCount, double errorRate)
        {
            if (errorRate > 80)
                return "Possible service outage or critical system failure";
            if (errorRate > 50)
                return "Possible deployment issue or configuration problem";
            if (errorRate > 30)
                return "Possible performance degradation or increased load";
            if (errorCount > 1000)
                return "Possible DDoS attack or automated scanning";

            return "Temporary increase in error rate";
        }
    }
}
