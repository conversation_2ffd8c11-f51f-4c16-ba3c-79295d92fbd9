using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using IISLogAnalyzer.Models;
using IISLogAnalyzer.Services;
using IISLogAnalyzer.Forms;
using IISLogAnalyzer.Utilities;

namespace IISLogAnalyzer
{
    public partial class MainForm : Form
    {
        private readonly IISLogParser _logParser;
        private readonly ApiAnalysisService _analysisService;
        private readonly ExportService _exportService;
        private OverallAnalysisSummary? _currentAnalysis;
        private List<IISLogEntry> _currentLogEntries = new();
        private FilterCriteria _currentFilter = new();
        private ApplicationSettings _settings;
        private readonly LoggingService _logger = LoggingService.Instance;

        public MainForm()
        {
            InitializeComponent();
            _logParser = new IISLogParser();
            _analysisService = new ApiAnalysisService();
            _exportService = new ExportService();
            _settings = ApplicationSettings.Load();

            // Subscribe to progress events
            _logParser.ProgressUpdated += OnProgressUpdated;
            _analysisService.ProgressUpdated += OnProgressUpdated;

            InitializeUI();
            ApplySettings();
        }

        private void InitializeUI()
        {
            // Set form properties
            this.Text = "IIS Log Analyzer";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(800, 600);

            // Enable double buffering for better performance
            this.SetStyle(ControlStyles.AllPaintingInWmPaint | ControlStyles.UserPaint | ControlStyles.DoubleBuffer, true);
        }

        private async void btnSelectFiles_Click(object sender, EventArgs e)
        {
            using var openFileDialog = new OpenFileDialog
            {
                Title = "Select IIS Log Files",
                Filter = "Log files (*.log)|*.log|All files (*.*)|*.*",
                Multiselect = true,
                CheckFileExists = true
            };

            if (openFileDialog.ShowDialog() == DialogResult.OK)
            {
                await ProcessLogFiles(openFileDialog.FileNames);
            }
        }

        private async void btnSelectFolder_Click(object sender, EventArgs e)
        {
            using var folderDialog = new FolderBrowserDialog
            {
                Description = "Select folder containing IIS log files",
                UseDescriptionForTitle = true
            };

            if (folderDialog.ShowDialog() == DialogResult.OK)
            {
                var logFiles = Directory.GetFiles(folderDialog.SelectedPath, "*.log", SearchOption.AllDirectories);
                if (logFiles.Length > 0)
                {
                    await ProcessLogFiles(logFiles);
                }
                else
                {
                    MessageBox.Show("No log files found in the selected folder.", "No Files Found",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private async Task ProcessLogFiles(string[] filePaths)
        {
            await ErrorHandler.ExecuteWithErrorHandlingAsync(async () =>
            {
                _logger.LogInfo($"Starting to process {filePaths.Length} log files");

                // Update UI state
                SetUIEnabled(false);
                progressBar.Value = 0;
                lblStatus.Text = "Parsing log files...";

                // Parse log files
                _currentLogEntries = await _logParser.ParseMultipleLogFilesAsync(filePaths);

                _logger.LogInfo($"Parsed {_currentLogEntries.Count} log entries from {filePaths.Length} files");

                if (_currentLogEntries.Count == 0)
                {
                    ErrorHandler.HandleWarning("No valid log entries found in the selected files.");
                    return;
                }

                lblStatus.Text = "Analyzing log data...";

                // Analyze the logs
                _currentAnalysis = await _analysisService.AnalyzeLogsAsync(_currentLogEntries);

                _logger.LogInfo($"Analysis complete. Found {_currentAnalysis.TotalApiEndpoints} API endpoints");

                // Update the results display
                DisplayResults();

                lblStatus.Text = $"Analysis complete. Processed {_currentLogEntries.Count:N0} log entries from {filePaths.Length} files.";

                // Save last directory if setting is enabled
                if (_settings.RememberLastDirectory && filePaths.Length > 0)
                {
                    _settings.LastDirectory = Path.GetDirectoryName(filePaths[0]) ?? string.Empty;
                    _settings.Save();
                }
            }, "Error processing log files");

            SetUIEnabled(true);
            progressBar.Value = 100;
        }

        private void DisplayResults()
        {
            if (_currentAnalysis == null) return;

            // Update summary labels
            lblTotalEntries.Text = $"Total Entries: {_currentAnalysis.TotalLogEntries:N0}";
            lblTotalApis.Text = $"Total APIs: {_currentAnalysis.TotalApiEndpoints:N0}";
            lblTimeSpan.Text = $"Time Span: {_currentAnalysis.LogTimeSpan.TotalHours:F1} hours";

            // Populate the results grid
            PopulateResultsGrid();

            // Enable export buttons
            btnExportCsv.Enabled = true;
            btnExportJson.Enabled = true;
            btnExportHtml.Enabled = true;
            btnShowCharts.Enabled = true;
        }

        private void PopulateResultsGrid()
        {
            if (_currentAnalysis == null) return;

            dataGridViewResults.Rows.Clear();

            // Apply current filters
            var filteredResults = _currentAnalysis.ApiResults
                .Where(r => _currentFilter.Matches(r))
                .OrderByDescending(r => r.TotalRequests);

            foreach (var result in filteredResults)
            {
                var row = new DataGridViewRow();
                row.CreateCells(dataGridViewResults);

                row.Cells[0].Value = result.ApiEndpoint;
                row.Cells[1].Value = result.Method;
                row.Cells[2].Value = result.TotalRequests;
                row.Cells[3].Value = $"{result.SuccessRate:F1}%";
                row.Cells[4].Value = $"{result.ErrorRate:F1}%";
                row.Cells[5].Value = $"{result.AverageResponseTime:F0} ms";
                row.Cells[6].Value = result.UniqueClientCount;
                row.Cells[7].Value = $"{result.RequestsPerHour:F1}";

                dataGridViewResults.Rows.Add(row);
            }

            // Update filter status
            UpdateFilterStatus();
        }

        private void OnProgressUpdated(object? sender, ProgressEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnProgressUpdated(sender, e)));
                return;
            }

            progressBar.Value = Math.Min(100, Math.Max(0, (int)e.ProgressPercentage));
            lblStatus.Text = e.Message;
        }

        private void SetUIEnabled(bool enabled)
        {
            btnSelectFiles.Enabled = enabled;
            btnSelectFolder.Enabled = enabled;
            btnExportCsv.Enabled = enabled && _currentAnalysis != null;
            btnExportJson.Enabled = enabled && _currentAnalysis != null;
            btnExportHtml.Enabled = enabled && _currentAnalysis != null;
            btnShowCharts.Enabled = enabled && _currentAnalysis != null;
        }

        private async void btnExportCsv_Click(object sender, EventArgs e)
        {
            if (_currentAnalysis == null) return;

            using var saveFileDialog = new SaveFileDialog
            {
                Title = "Export to CSV",
                Filter = "CSV files (*.csv)|*.csv",
                DefaultExt = "csv",
                FileName = $"IIS_Analysis_{DateTime.Now:yyyyMMdd_HHmmss}.csv",
                InitialDirectory = _settings.RememberLastDirectory ? _settings.LastDirectory : string.Empty
            };

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                await ErrorHandler.ExecuteWithErrorHandlingAsync(async () =>
                {
                    _logger.LogInfo($"Exporting analysis results to CSV: {saveFileDialog.FileName}");
                    await _exportService.ExportToCsvAsync(_currentAnalysis, saveFileDialog.FileName);
                    _logger.LogInfo("CSV export completed successfully");

                    MessageBox.Show("Export completed successfully!", "Export Complete",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }, "Error exporting to CSV");
            }
        }

        private async void btnExportJson_Click(object sender, EventArgs e)
        {
            if (_currentAnalysis == null) return;

            using var saveFileDialog = new SaveFileDialog
            {
                Title = "Export to JSON",
                Filter = "JSON files (*.json)|*.json",
                DefaultExt = "json",
                FileName = $"IIS_Analysis_{DateTime.Now:yyyyMMdd_HHmmss}.json"
            };

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    await _exportService.ExportToJsonAsync(_currentAnalysis, saveFileDialog.FileName);
                    MessageBox.Show("Export completed successfully!", "Export Complete",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error exporting to JSON: {ex.Message}", "Export Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private async void btnExportHtml_Click(object sender, EventArgs e)
        {
            if (_currentAnalysis == null) return;

            using var saveFileDialog = new SaveFileDialog
            {
                Title = "Export to HTML Report",
                Filter = "HTML files (*.html)|*.html",
                DefaultExt = "html",
                FileName = $"IIS_Analysis_Report_{DateTime.Now:yyyyMMdd_HHmmss}.html"
            };

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    await _exportService.ExportToHtmlAsync(_currentAnalysis, saveFileDialog.FileName);
                    MessageBox.Show("Export completed successfully!", "Export Complete",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error exporting to HTML: {ex.Message}", "Export Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void txtFilter_TextChanged(object sender, EventArgs e)
        {
            if (_currentAnalysis == null) return;

            var filterText = txtFilter.Text.ToLower();
            if (string.IsNullOrWhiteSpace(filterText))
            {
                PopulateResultsGrid();
                return;
            }

            // Filter results based on API endpoint or method
            var filteredResults = _currentAnalysis.ApiResults
                .Where(r => r.ApiEndpoint.ToLower().Contains(filterText) ||
                           r.Method.ToLower().Contains(filterText))
                .OrderByDescending(r => r.TotalRequests)
                .ToList();

            dataGridViewResults.Rows.Clear();

            foreach (var result in filteredResults)
            {
                var row = new DataGridViewRow();
                row.CreateCells(dataGridViewResults);

                row.Cells[0].Value = result.ApiEndpoint;
                row.Cells[1].Value = result.Method;
                row.Cells[2].Value = result.TotalRequests;
                row.Cells[3].Value = $"{result.SuccessRate:F1}%";
                row.Cells[4].Value = $"{result.ErrorRate:F1}%";
                row.Cells[5].Value = $"{result.AverageResponseTime:F0} ms";
                row.Cells[6].Value = result.UniqueClientCount;
                row.Cells[7].Value = $"{result.RequestsPerHour:F1}";

                dataGridViewResults.Rows.Add(row);
            }
        }

        private void btnAdvancedFilter_Click(object sender, EventArgs e)
        {
            using var filterForm = new AdvancedFilterForm(_currentFilter);
            if (filterForm.ShowDialog() == DialogResult.OK)
            {
                _currentFilter = filterForm.FilterCriteria;
                PopulateResultsGrid();
            }
        }

        private void btnClearFilters_Click(object sender, EventArgs e)
        {
            _currentFilter = new FilterCriteria();
            txtFilter.Clear();
            PopulateResultsGrid();
        }

        private void UpdateFilterStatus()
        {
            if (_currentFilter.HasActiveFilters())
            {
                lblFilterStatus.Text = $"Filters: {_currentFilter.GetFilterSummary()}";
                lblFilterStatus.ForeColor = Color.Blue;
                btnClearFilters.Enabled = true;
            }
            else
            {
                lblFilterStatus.Text = "No filters applied";
                lblFilterStatus.ForeColor = Color.Gray;
                btnClearFilters.Enabled = false;
            }
        }

        private void btnShowCharts_Click(object sender, EventArgs e)
        {
            if (_currentAnalysis == null) return;

            try
            {
                using var chartsForm = new ChartsForm(_currentAnalysis);
                chartsForm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error displaying charts: {ex.Message}", "Charts Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnSettings_Click(object sender, EventArgs e)
        {
            using var settingsForm = new SettingsForm(_settings);
            if (settingsForm.ShowDialog() == DialogResult.OK)
            {
                _settings = settingsForm.Settings;
                _settings.Save();
                ApplySettings();
                MessageBox.Show("Settings have been applied successfully!", "Settings",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void ApplySettings()
        {
            // Apply window settings
            if (_settings.WindowSettings.Width > 0 && _settings.WindowSettings.Height > 0)
            {
                this.Size = new Size(_settings.WindowSettings.Width, _settings.WindowSettings.Height);
            }

            if (_settings.WindowSettings.X >= 0 && _settings.WindowSettings.Y >= 0)
            {
                this.Location = new Point(_settings.WindowSettings.X, _settings.WindowSettings.Y);
            }

            if (_settings.WindowSettings.Maximized)
            {
                this.WindowState = FormWindowState.Maximized;
            }

            // Apply color settings to existing results if available
            if (_currentAnalysis != null)
            {
                PopulateResultsGrid();
            }
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // Save window settings
            if (this.WindowState == FormWindowState.Normal)
            {
                _settings.WindowSettings.Width = this.Width;
                _settings.WindowSettings.Height = this.Height;
                _settings.WindowSettings.X = this.Location.X;
                _settings.WindowSettings.Y = this.Location.Y;
                _settings.WindowSettings.Maximized = false;
            }
            else if (this.WindowState == FormWindowState.Maximized)
            {
                _settings.WindowSettings.Maximized = true;
            }

            _settings.Save();
            base.OnFormClosing(e);
        }

        private void viewLogToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ErrorHandler.ExecuteWithErrorHandling(() =>
            {
                var logPath = _logger.GetLogFilePath();
                if (File.Exists(logPath))
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = logPath,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("Log file not found.", "Log File",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }, "Error opening log file");
        }

        private void aboutToolStripMenuItem_Click(object sender, EventArgs e)
        {
            var version = System.Reflection.Assembly.GetExecutingAssembly().GetName().Version;
            var aboutMessage = $"IIS Log Analyzer\n\n" +
                              $"Version: {version}\n" +
                              $"Built with .NET 9.0\n\n" +
                              $"A powerful tool for analyzing IIS log files and generating comprehensive API-wise reports.\n\n" +
                              $"Features:\n" +
                              $"• Parse IIS W3C Extended Log Format\n" +
                              $"• API-wise analysis and metrics\n" +
                              $"• Advanced filtering and search\n" +
                              $"• Data visualization\n" +
                              $"• Multiple export formats\n" +
                              $"• Configurable settings\n\n" +
                              $"© 2025 IIS Log Analyzer";

            MessageBox.Show(aboutMessage, "About IIS Log Analyzer",
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
    }
}
