using System;
using System.IO;
using System.Text;

namespace IISLogAnalyzer.Services
{
    /// <summary>
    /// Service for application logging and error tracking
    /// </summary>
    public class LoggingService
    {
        private static LoggingService? _instance;
        private static readonly object _lock = new object();
        private readonly string _logFilePath;
        private readonly object _fileLock = new object();

        private LoggingService()
        {
            var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var logDirectory = Path.Combine(appDataPath, "IISLogAnalyzer", "Logs");
            
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }

            _logFilePath = Path.Combine(logDirectory, $"IISLogAnalyzer_{DateTime.Now:yyyyMMdd}.log");
        }

        /// <summary>
        /// Get the singleton instance of the logging service
        /// </summary>
        public static LoggingService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new LoggingService();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Log levels for different types of messages
        /// </summary>
        public enum LogLevel
        {
            Debug,
            Info,
            Warning,
            Error,
            Fatal
        }

        /// <summary>
        /// Log a message with the specified level
        /// </summary>
        public void Log(LogLevel level, string message, Exception? exception = null)
        {
            try
            {
                var logEntry = FormatLogEntry(level, message, exception);
                
                lock (_fileLock)
                {
                    File.AppendAllText(_logFilePath, logEntry + Environment.NewLine, Encoding.UTF8);
                }

                // Also write to debug output in debug builds
                #if DEBUG
                System.Diagnostics.Debug.WriteLine(logEntry);
                #endif
            }
            catch
            {
                // Silently fail if logging fails to prevent infinite loops
            }
        }

        /// <summary>
        /// Log debug information
        /// </summary>
        public void LogDebug(string message)
        {
            Log(LogLevel.Debug, message);
        }

        /// <summary>
        /// Log informational message
        /// </summary>
        public void LogInfo(string message)
        {
            Log(LogLevel.Info, message);
        }

        /// <summary>
        /// Log warning message
        /// </summary>
        public void LogWarning(string message, Exception? exception = null)
        {
            Log(LogLevel.Warning, message, exception);
        }

        /// <summary>
        /// Log error message
        /// </summary>
        public void LogError(string message, Exception? exception = null)
        {
            Log(LogLevel.Error, message, exception);
        }

        /// <summary>
        /// Log fatal error message
        /// </summary>
        public void LogFatal(string message, Exception? exception = null)
        {
            Log(LogLevel.Fatal, message, exception);
        }

        /// <summary>
        /// Log an exception with full details
        /// </summary>
        public void LogException(Exception exception, string? additionalMessage = null)
        {
            var message = string.IsNullOrEmpty(additionalMessage) 
                ? "Unhandled exception occurred" 
                : additionalMessage;
            
            Log(LogLevel.Error, message, exception);
        }

        /// <summary>
        /// Format a log entry with timestamp, level, and message
        /// </summary>
        private string FormatLogEntry(LogLevel level, string message, Exception? exception)
        {
            var sb = new StringBuilder();
            
            // Timestamp
            sb.Append($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] ");
            
            // Log level
            sb.Append($"[{level.ToString().ToUpper()}] ");
            
            // Message
            sb.Append(message);
            
            // Exception details if provided
            if (exception != null)
            {
                sb.AppendLine();
                sb.Append($"Exception: {exception.GetType().Name}: {exception.Message}");
                
                if (!string.IsNullOrEmpty(exception.StackTrace))
                {
                    sb.AppendLine();
                    sb.Append($"Stack Trace: {exception.StackTrace}");
                }
                
                // Inner exceptions
                var innerException = exception.InnerException;
                while (innerException != null)
                {
                    sb.AppendLine();
                    sb.Append($"Inner Exception: {innerException.GetType().Name}: {innerException.Message}");
                    innerException = innerException.InnerException;
                }
            }
            
            return sb.ToString();
        }

        /// <summary>
        /// Get the current log file path
        /// </summary>
        public string GetLogFilePath()
        {
            return _logFilePath;
        }

        /// <summary>
        /// Clear old log files (keep only last 30 days)
        /// </summary>
        public void CleanupOldLogs()
        {
            try
            {
                var logDirectory = Path.GetDirectoryName(_logFilePath);
                if (string.IsNullOrEmpty(logDirectory) || !Directory.Exists(logDirectory))
                    return;

                var cutoffDate = DateTime.Now.AddDays(-30);
                var logFiles = Directory.GetFiles(logDirectory, "IISLogAnalyzer_*.log");

                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        try
                        {
                            File.Delete(logFile);
                            LogInfo($"Deleted old log file: {Path.GetFileName(logFile)}");
                        }
                        catch (Exception ex)
                        {
                            LogWarning($"Failed to delete old log file {Path.GetFileName(logFile)}: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogWarning($"Failed to cleanup old logs: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Get recent log entries for display
        /// </summary>
        public string GetRecentLogs(int maxLines = 100)
        {
            try
            {
                if (!File.Exists(_logFilePath))
                    return "No log entries found.";

                var lines = File.ReadAllLines(_logFilePath);
                var recentLines = lines.Length > maxLines 
                    ? lines.Skip(lines.Length - maxLines).ToArray()
                    : lines;

                return string.Join(Environment.NewLine, recentLines);
            }
            catch (Exception ex)
            {
                return $"Error reading log file: {ex.Message}";
            }
        }

        /// <summary>
        /// Log application startup information
        /// </summary>
        public void LogApplicationStart()
        {
            LogInfo("=== IIS Log Analyzer Started ===");
            LogInfo($"Version: {System.Reflection.Assembly.GetExecutingAssembly().GetName().Version}");
            LogInfo($"OS: {Environment.OSVersion}");
            LogInfo($".NET Version: {Environment.Version}");
            LogInfo($"Working Directory: {Environment.CurrentDirectory}");
            LogInfo($"User: {Environment.UserName}");
            LogInfo($"Machine: {Environment.MachineName}");
        }

        /// <summary>
        /// Log application shutdown information
        /// </summary>
        public void LogApplicationShutdown()
        {
            LogInfo("=== IIS Log Analyzer Shutdown ===");
        }
    }
}
