using System;
using System.Collections.Generic;
using System.Linq;

namespace IISLogAnalyzer.Models
{
    /// <summary>
    /// Performance analysis data models
    /// </summary>
    public class PerformanceAnalysis
    {
        public TrafficAnalysis TrafficAnalysis { get; set; } = new();
        public ResponseTimeAnalysis ResponseTimeAnalysis { get; set; } = new();
        public BandwidthAnalysis BandwidthAnalysis { get; set; } = new();
        public List<EndpointPerformance> SlowestEndpoints { get; set; } = new();
    }

    /// <summary>
    /// Traffic pattern analysis
    /// </summary>
    public class TrafficAnalysis
    {
        public Dictionary<DateTime, int> HourlyTraffic { get; set; } = new();
        public Dictionary<DateTime, int> MinutelyTraffic { get; set; } = new();
        public PeakTrafficInfo PeakHour { get; set; } = new();
        public PeakTrafficInfo PeakMinute { get; set; } = new();
        public double AverageRequestsPerHour { get; set; }
        public double AverageRequestsPerMinute { get; set; }
    }

    /// <summary>
    /// Peak traffic information
    /// </summary>
    public class PeakTrafficInfo
    {
        public DateTime TimeStamp { get; set; }
        public int RequestCount { get; set; }
        public double PercentageOfTotal { get; set; }
    }

    /// <summary>
    /// Response time percentile analysis
    /// </summary>
    public class ResponseTimeAnalysis
    {
        public double P50 { get; set; } // Median
        public double P90 { get; set; }
        public double P95 { get; set; }
        public double P99 { get; set; }
        public double Average { get; set; }
        public int Minimum { get; set; }
        public int Maximum { get; set; }
        public double StandardDeviation { get; set; }
        public Dictionary<string, ResponseTimePercentiles> ByEndpoint { get; set; } = new();
    }

    /// <summary>
    /// Response time percentiles for a specific endpoint
    /// </summary>
    public class ResponseTimePercentiles
    {
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public double P50 { get; set; }
        public double P90 { get; set; }
        public double P95 { get; set; }
        public double P99 { get; set; }
        public double Average { get; set; }
        public int SampleCount { get; set; }
    }

    /// <summary>
    /// Bandwidth usage analysis
    /// </summary>
    public class BandwidthAnalysis
    {
        public Dictionary<DateTime, long> HourlyBytesSent { get; set; } = new();
        public Dictionary<DateTime, long> HourlyBytesReceived { get; set; } = new();
        public long TotalBytesSent { get; set; }
        public long TotalBytesReceived { get; set; }
        public double AverageBytesPerRequest { get; set; }
        public PeakBandwidthInfo PeakSentHour { get; set; } = new();
        public PeakBandwidthInfo PeakReceivedHour { get; set; } = new();
        public List<EndpointBandwidth> TopBandwidthConsumers { get; set; } = new();
    }

    /// <summary>
    /// Peak bandwidth information
    /// </summary>
    public class PeakBandwidthInfo
    {
        public DateTime TimeStamp { get; set; }
        public long Bytes { get; set; }
        public string FormattedSize { get; set; } = string.Empty;
    }

    /// <summary>
    /// Bandwidth usage by endpoint
    /// </summary>
    public class EndpointBandwidth
    {
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public long TotalBytesSent { get; set; }
        public long TotalBytesReceived { get; set; }
        public double AverageBytesPerRequest { get; set; }
        public int RequestCount { get; set; }
        public string FormattedTotalSent { get; set; } = string.Empty;
        public string FormattedTotalReceived { get; set; } = string.Empty;
    }

    /// <summary>
    /// Detailed endpoint performance analysis
    /// </summary>
    public class EndpointPerformance
    {
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public int TotalRequests { get; set; }
        public double AverageResponseTime { get; set; }
        public int MinResponseTime { get; set; }
        public int MaxResponseTime { get; set; }
        public ResponseTimePercentiles Percentiles { get; set; } = new();
        public Dictionary<int, int> ResponseTimeDistribution { get; set; } = new(); // Time ranges to count
        public List<SlowRequest> SlowestRequests { get; set; } = new();
        public double ErrorRate { get; set; }
        public long TotalBandwidth { get; set; }
    }

    /// <summary>
    /// Individual slow request details
    /// </summary>
    public class SlowRequest
    {
        public DateTime TimeStamp { get; set; }
        public string ClientIP { get; set; } = string.Empty;
        public int ResponseTime { get; set; }
        public int StatusCode { get; set; }
        public long BytesSent { get; set; }
        public string UserAgent { get; set; } = string.Empty;
        public string FullUri { get; set; } = string.Empty;
    }
}
