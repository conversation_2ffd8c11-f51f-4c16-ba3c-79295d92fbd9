using System;

namespace IISLogAnalyzer.Models
{
    /// <summary>
    /// Represents a single IIS log entry parsed from W3C Extended Log Format
    /// </summary>
    public class IISLogEntry
    {
        public DateTime Date { get; set; }
        public TimeSpan Time { get; set; }
        public DateTime DateTime => Date.Add(Time);
        public string ServerIP { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public string UriStem { get; set; } = string.Empty;
        public string UriQuery { get; set; } = string.Empty;
        public int Port { get; set; }
        public string Username { get; set; } = string.Empty;
        public string ClientIP { get; set; } = string.Empty;
        public string UserAgent { get; set; } = string.Empty;
        public string Referer { get; set; } = string.Empty;
        public int StatusCode { get; set; }
        public int SubStatusCode { get; set; }
        public int Win32StatusCode { get; set; }
        public long BytesSent { get; set; }
        public long BytesReceived { get; set; }
        public int TimeTaken { get; set; } // in milliseconds
        
        /// <summary>
        /// Gets the full URI including query string
        /// </summary>
        public string FullUri => string.IsNullOrEmpty(UriQuery) ? UriStem : $"{UriStem}?{UriQuery}";
        
        /// <summary>
        /// Gets the API endpoint (UriStem without query parameters)
        /// </summary>
        public string ApiEndpoint => UriStem;
        
        /// <summary>
        /// Determines if this is a successful request (2xx status codes)
        /// </summary>
        public bool IsSuccessful => StatusCode >= 200 && StatusCode < 300;
        
        /// <summary>
        /// Determines if this is a client error (4xx status codes)
        /// </summary>
        public bool IsClientError => StatusCode >= 400 && StatusCode < 500;
        
        /// <summary>
        /// Determines if this is a server error (5xx status codes)
        /// </summary>
        public bool IsServerError => StatusCode >= 500 && StatusCode < 600;
    }
}
