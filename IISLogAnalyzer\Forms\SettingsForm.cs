using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Forms
{
    /// <summary>
    /// Form for configuring application settings
    /// </summary>
    public partial class SettingsForm : Form
    {
        private ApplicationSettings _settings;

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public ApplicationSettings Settings
        {
            get => _settings;
            set => _settings = value;
        }

        public SettingsForm(ApplicationSettings settings)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            InitializeComponent();
            LoadSettings();
        }

        private void LoadSettings()
        {
            // Analysis Settings
            numericUpDownTopApiCount.Value = _settings.DefaultTopApiCount;
            numericUpDownSlowThreshold.Value = _settings.SlowApiThreshold;
            numericUpDownErrorThreshold.Value = (decimal)_settings.ErrorRateThreshold;
            numericUpDownAutoRefresh.Value = _settings.AutoRefreshInterval;
            numericUpDownMaxEntries.Value = _settings.MaxLogEntries;

            // Export Settings
            comboBoxDefaultExport.Text = _settings.DefaultExportFormat;
            checkBoxRememberDirectory.Checked = _settings.RememberLastDirectory;

            // Performance Settings
            numericUpDownBatchSize.Value = _settings.PerformanceSettings.BatchSize;
            checkBoxProgressReporting.Checked = _settings.PerformanceSettings.EnableProgressReporting;
            numericUpDownProgressInterval.Value = _settings.PerformanceSettings.ProgressUpdateInterval;
            checkBoxMemoryOptimization.Checked = _settings.PerformanceSettings.EnableMemoryOptimization;
            numericUpDownCacheSize.Value = _settings.PerformanceSettings.MaxCacheSize;

            // Color Settings
            panelSuccessColor.BackColor = ColorTranslator.FromHtml(_settings.ColorSettings.SuccessColor);
            panelWarningColor.BackColor = ColorTranslator.FromHtml(_settings.ColorSettings.WarningColor);
            panelErrorColor.BackColor = ColorTranslator.FromHtml(_settings.ColorSettings.ErrorColor);
            panelInfoColor.BackColor = ColorTranslator.FromHtml(_settings.ColorSettings.InfoColor);
            numericUpDownSuccessThreshold.Value = (decimal)_settings.ColorSettings.SuccessThreshold;
            numericUpDownWarningThreshold.Value = (decimal)_settings.ColorSettings.WarningThreshold;
        }

        private void SaveSettings()
        {
            // Analysis Settings
            _settings.DefaultTopApiCount = (int)numericUpDownTopApiCount.Value;
            _settings.SlowApiThreshold = (int)numericUpDownSlowThreshold.Value;
            _settings.ErrorRateThreshold = (double)numericUpDownErrorThreshold.Value;
            _settings.AutoRefreshInterval = (int)numericUpDownAutoRefresh.Value;
            _settings.MaxLogEntries = (int)numericUpDownMaxEntries.Value;

            // Export Settings
            _settings.DefaultExportFormat = comboBoxDefaultExport.Text;
            _settings.RememberLastDirectory = checkBoxRememberDirectory.Checked;

            // Performance Settings
            _settings.PerformanceSettings.BatchSize = (int)numericUpDownBatchSize.Value;
            _settings.PerformanceSettings.EnableProgressReporting = checkBoxProgressReporting.Checked;
            _settings.PerformanceSettings.ProgressUpdateInterval = (int)numericUpDownProgressInterval.Value;
            _settings.PerformanceSettings.EnableMemoryOptimization = checkBoxMemoryOptimization.Checked;
            _settings.PerformanceSettings.MaxCacheSize = (int)numericUpDownCacheSize.Value;

            // Color Settings
            _settings.ColorSettings.SuccessColor = ColorTranslator.ToHtml(panelSuccessColor.BackColor);
            _settings.ColorSettings.WarningColor = ColorTranslator.ToHtml(panelWarningColor.BackColor);
            _settings.ColorSettings.ErrorColor = ColorTranslator.ToHtml(panelErrorColor.BackColor);
            _settings.ColorSettings.InfoColor = ColorTranslator.ToHtml(panelInfoColor.BackColor);
            _settings.ColorSettings.SuccessThreshold = (double)numericUpDownSuccessThreshold.Value;
            _settings.ColorSettings.WarningThreshold = (double)numericUpDownWarningThreshold.Value;
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            SaveSettings();
            DialogResult = DialogResult.OK;
            Close();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private void btnApply_Click(object sender, EventArgs e)
        {
            SaveSettings();
            _settings.Save();
            MessageBox.Show("Settings applied successfully!", "Settings", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnResetDefaults_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "Are you sure you want to reset all settings to their default values?",
                "Reset Settings",
                MessageBoxButtons.YesNo,
                MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                _settings.ResetToDefaults();
                LoadSettings();
            }
        }

        private void panelSuccessColor_Click(object sender, EventArgs e)
        {
            ChooseColor(panelSuccessColor);
        }

        private void panelWarningColor_Click(object sender, EventArgs e)
        {
            ChooseColor(panelWarningColor);
        }

        private void panelErrorColor_Click(object sender, EventArgs e)
        {
            ChooseColor(panelErrorColor);
        }

        private void panelInfoColor_Click(object sender, EventArgs e)
        {
            ChooseColor(panelInfoColor);
        }

        private void ChooseColor(Panel panel)
        {
            using var colorDialog = new ColorDialog
            {
                Color = panel.BackColor,
                FullOpen = true
            };

            if (colorDialog.ShowDialog() == DialogResult.OK)
            {
                panel.BackColor = colorDialog.Color;
            }
        }

        private void checkBoxProgressReporting_CheckedChanged(object sender, EventArgs e)
        {
            numericUpDownProgressInterval.Enabled = checkBoxProgressReporting.Checked;
        }

        private void numericUpDownAutoRefresh_ValueChanged(object sender, EventArgs e)
        {
            if (numericUpDownAutoRefresh.Value > 0)
            {
                lblAutoRefreshNote.Text = "Note: Auto-refresh will reload analysis periodically";
                lblAutoRefreshNote.ForeColor = Color.Blue;
            }
            else
            {
                lblAutoRefreshNote.Text = "Auto-refresh disabled";
                lblAutoRefreshNote.ForeColor = Color.Gray;
            }
        }

        private void numericUpDownMaxEntries_ValueChanged(object sender, EventArgs e)
        {
            if (numericUpDownMaxEntries.Value > 0)
            {
                lblMaxEntriesNote.Text = $"Will process only the first {numericUpDownMaxEntries.Value:N0} entries";
                lblMaxEntriesNote.ForeColor = Color.Orange;
            }
            else
            {
                lblMaxEntriesNote.Text = "All entries will be processed";
                lblMaxEntriesNote.ForeColor = Color.Green;
            }
        }

        private void btnTestColors_Click(object sender, EventArgs e)
        {
            var testForm = new Form
            {
                Text = "Color Test",
                Size = new Size(400, 300),
                StartPosition = FormStartPosition.CenterParent
            };

            var listView = new ListView
            {
                Dock = DockStyle.Fill,
                View = View.Details,
                FullRowSelect = true,
                GridLines = true
            };

            listView.Columns.Add("Status", 100);
            listView.Columns.Add("Description", 250);

            var items = new[]
            {
                new { Status = "Success", Description = "API with high success rate", Color = panelSuccessColor.BackColor },
                new { Status = "Warning", Description = "API with moderate success rate", Color = panelWarningColor.BackColor },
                new { Status = "Error", Description = "API with low success rate", Color = panelErrorColor.BackColor },
                new { Status = "Info", Description = "General information", Color = panelInfoColor.BackColor }
            };

            foreach (var item in items)
            {
                var listItem = new ListViewItem(new[] { item.Status, item.Description })
                {
                    BackColor = item.Color
                };
                listView.Items.Add(listItem);
            }

            testForm.Controls.Add(listView);
            testForm.ShowDialog();
        }
    }
}
