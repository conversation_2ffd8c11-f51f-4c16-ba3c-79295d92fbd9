using System;
using System.Collections.Generic;
using System.Linq;

namespace IISLogAnalyzer.Models
{
    /// <summary>
    /// Represents analysis results for a specific API endpoint
    /// </summary>
    public class ApiAnalysisResult
    {
        public string ApiEndpoint { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public int TotalRequests { get; set; }
        public int SuccessfulRequests { get; set; }
        public int ClientErrors { get; set; }
        public int ServerErrors { get; set; }
        public double AverageResponseTime { get; set; } // in milliseconds
        public int MinResponseTime { get; set; }
        public int MaxResponseTime { get; set; }
        public long TotalBytesSent { get; set; }
        public long TotalBytesReceived { get; set; }
        public DateTime FirstRequest { get; set; }
        public DateTime LastRequest { get; set; }
        public Dictionary<int, int> StatusCodeDistribution { get; set; } = new();
        public List<string> UniqueClientIPs { get; set; } = new();
        
        /// <summary>
        /// Success rate as a percentage
        /// </summary>
        public double SuccessRate => TotalRequests > 0 ? (double)SuccessfulRequests / TotalRequests * 100 : 0;
        
        /// <summary>
        /// Error rate as a percentage
        /// </summary>
        public double ErrorRate => TotalRequests > 0 ? (double)(ClientErrors + ServerErrors) / TotalRequests * 100 : 0;
        
        /// <summary>
        /// Average bytes sent per request
        /// </summary>
        public double AverageBytesSent => TotalRequests > 0 ? (double)TotalBytesSent / TotalRequests : 0;
        
        /// <summary>
        /// Average bytes received per request
        /// </summary>
        public double AverageBytesReceived => TotalRequests > 0 ? (double)TotalBytesReceived / TotalRequests : 0;
        
        /// <summary>
        /// Number of unique clients that accessed this endpoint
        /// </summary>
        public int UniqueClientCount => UniqueClientIPs.Count;
        
        /// <summary>
        /// Requests per hour based on time span
        /// </summary>
        public double RequestsPerHour
        {
            get
            {
                if (TotalRequests == 0 || FirstRequest == LastRequest)
                    return 0;
                
                var timeSpan = LastRequest - FirstRequest;
                return timeSpan.TotalHours > 0 ? TotalRequests / timeSpan.TotalHours : TotalRequests;
            }
        }
    }
    
    /// <summary>
    /// Represents overall analysis summary with enhanced analytics
    /// </summary>
    public class OverallAnalysisSummary
    {
        public int TotalLogEntries { get; set; }
        public int TotalApiEndpoints { get; set; }
        public DateTime AnalysisStartTime { get; set; }
        public DateTime AnalysisEndTime { get; set; }
        public TimeSpan LogTimeSpan { get; set; }
        public List<ApiAnalysisResult> ApiResults { get; set; } = new();
        public Dictionary<string, int> MethodDistribution { get; set; } = new();
        public Dictionary<int, int> OverallStatusCodeDistribution { get; set; } = new();

        // Enhanced analysis features
        public PerformanceAnalysis PerformanceAnalysis { get; set; } = new();
        public SecurityAnalysis SecurityAnalysis { get; set; } = new();
        public UserBehaviorAnalysis UserBehaviorAnalysis { get; set; } = new();
        public ErrorAnalysis ErrorAnalysis { get; set; } = new();
        public ApiUsageAnalytics ApiUsageAnalytics { get; set; } = new();
        
        /// <summary>
        /// Top APIs by request count
        /// </summary>
        public List<ApiAnalysisResult> TopApisByRequests => 
            ApiResults.OrderByDescending(a => a.TotalRequests).Take(10).ToList();
        
        /// <summary>
        /// Top APIs by error rate
        /// </summary>
        public List<ApiAnalysisResult> TopApisByErrorRate => 
            ApiResults.Where(a => a.TotalRequests > 0).OrderByDescending(a => a.ErrorRate).Take(10).ToList();
        
        /// <summary>
        /// Slowest APIs by average response time
        /// </summary>
        public List<ApiAnalysisResult> SlowestApis => 
            ApiResults.OrderByDescending(a => a.AverageResponseTime).Take(10).ToList();
    }
}
