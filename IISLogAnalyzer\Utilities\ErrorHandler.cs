using System;
using System.Windows.Forms;
using IISLogAnalyzer.Services;

namespace IISLogAnalyzer.Utilities
{
    /// <summary>
    /// Centralized error handling utility
    /// </summary>
    public static class ErrorHandler
    {
        private static readonly LoggingService _logger = LoggingService.Instance;

        /// <summary>
        /// Handle and display an error to the user
        /// </summary>
        public static void HandleError(Exception exception, string userMessage = "An error occurred", bool showToUser = true)
        {
            // Log the error
            _logger.LogException(exception, userMessage);

            // Show to user if requested
            if (showToUser)
            {
                ShowErrorDialog(userMessage, exception);
            }
        }

        /// <summary>
        /// Handle and display an error with a custom message
        /// </summary>
        public static void HandleError(string message, Exception? exception = null, bool showToUser = true)
        {
            // Log the error
            if (exception != null)
            {
                _logger.LogError(message, exception);
            }
            else
            {
                _logger.LogError(message);
            }

            // Show to user if requested
            if (showToUser)
            {
                ShowErrorDialog(message, exception);
            }
        }

        /// <summary>
        /// Handle a warning that should be shown to the user
        /// </summary>
        public static void HandleWarning(string message, Exception? exception = null, bool showToUser = true)
        {
            // Log the warning
            if (exception != null)
            {
                _logger.LogWarning(message, exception);
            }
            else
            {
                _logger.LogWarning(message);
            }

            // Show to user if requested
            if (showToUser)
            {
                MessageBox.Show(message, "Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// Execute an action with error handling
        /// </summary>
        public static void ExecuteWithErrorHandling(Action action, string errorMessage = "An error occurred")
        {
            try
            {
                action();
            }
            catch (Exception ex)
            {
                HandleError(ex, errorMessage);
            }
        }

        /// <summary>
        /// Execute an action with error handling and return a result
        /// </summary>
        public static T? ExecuteWithErrorHandling<T>(Func<T> func, string errorMessage = "An error occurred", T? defaultValue = default)
        {
            try
            {
                return func();
            }
            catch (Exception ex)
            {
                HandleError(ex, errorMessage);
                return defaultValue;
            }
        }

        /// <summary>
        /// Execute an async action with error handling
        /// </summary>
        public static async System.Threading.Tasks.Task ExecuteWithErrorHandlingAsync(
            Func<System.Threading.Tasks.Task> asyncAction, 
            string errorMessage = "An error occurred")
        {
            try
            {
                await asyncAction();
            }
            catch (Exception ex)
            {
                HandleError(ex, errorMessage);
            }
        }

        /// <summary>
        /// Execute an async function with error handling and return a result
        /// </summary>
        public static async System.Threading.Tasks.Task<T?> ExecuteWithErrorHandlingAsync<T>(
            Func<System.Threading.Tasks.Task<T>> asyncFunc, 
            string errorMessage = "An error occurred", 
            T? defaultValue = default)
        {
            try
            {
                return await asyncFunc();
            }
            catch (Exception ex)
            {
                HandleError(ex, errorMessage);
                return defaultValue;
            }
        }

        /// <summary>
        /// Show a detailed error dialog to the user
        /// </summary>
        private static void ShowErrorDialog(string message, Exception? exception)
        {
            var errorForm = new Form
            {
                Text = "Error",
                Size = new System.Drawing.Size(500, 400),
                StartPosition = FormStartPosition.CenterParent,
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var tableLayout = new TableLayoutPanel
            {
                Dock = DockStyle.Fill,
                RowCount = 3,
                ColumnCount = 1,
                Padding = new Padding(10)
            };

            tableLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));
            tableLayout.RowStyles.Add(new RowStyle(SizeType.Percent, 100));
            tableLayout.RowStyles.Add(new RowStyle(SizeType.AutoSize));

            // Error message
            var messageLabel = new Label
            {
                Text = message,
                AutoSize = true,
                Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Bold),
                ForeColor = System.Drawing.Color.DarkRed,
                Dock = DockStyle.Top
            };

            // Error details
            var detailsTextBox = new TextBox
            {
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                Dock = DockStyle.Fill,
                Font = new System.Drawing.Font("Consolas", 8F)
            };

            if (exception != null)
            {
                detailsTextBox.Text = $"Exception Type: {exception.GetType().Name}\r\n" +
                                     $"Message: {exception.Message}\r\n\r\n" +
                                     $"Stack Trace:\r\n{exception.StackTrace}";

                if (exception.InnerException != null)
                {
                    detailsTextBox.Text += $"\r\n\r\nInner Exception:\r\n{exception.InnerException}";
                }
            }
            else
            {
                detailsTextBox.Text = "No additional details available.";
            }

            // Buttons
            var buttonPanel = new FlowLayoutPanel
            {
                FlowDirection = FlowDirection.RightToLeft,
                Dock = DockStyle.Bottom,
                Height = 35
            };

            var okButton = new Button
            {
                Text = "OK",
                DialogResult = DialogResult.OK,
                Size = new System.Drawing.Size(75, 25)
            };

            var copyButton = new Button
            {
                Text = "Copy Details",
                Size = new System.Drawing.Size(85, 25)
            };

            copyButton.Click += (s, e) =>
            {
                try
                {
                    var clipboardText = $"Error: {message}\r\n\r\n{detailsTextBox.Text}";
                    Clipboard.SetText(clipboardText);
                    MessageBox.Show("Error details copied to clipboard.", "Copied", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch
                {
                    MessageBox.Show("Failed to copy to clipboard.", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            };

            var viewLogButton = new Button
            {
                Text = "View Log",
                Size = new System.Drawing.Size(75, 25)
            };

            viewLogButton.Click += (s, e) =>
            {
                try
                {
                    var logPath = _logger.GetLogFilePath();
                    if (System.IO.File.Exists(logPath))
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = logPath,
                            UseShellExecute = true
                        });
                    }
                    else
                    {
                        MessageBox.Show("Log file not found.", "Error", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Failed to open log file: {ex.Message}", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            };

            buttonPanel.Controls.Add(okButton);
            buttonPanel.Controls.Add(copyButton);
            buttonPanel.Controls.Add(viewLogButton);

            tableLayout.Controls.Add(messageLabel, 0, 0);
            tableLayout.Controls.Add(detailsTextBox, 0, 1);
            tableLayout.Controls.Add(buttonPanel, 0, 2);

            errorForm.Controls.Add(tableLayout);
            errorForm.AcceptButton = okButton;
            errorForm.ShowDialog();
        }

        /// <summary>
        /// Set up global exception handling for the application
        /// </summary>
        public static void SetupGlobalExceptionHandling()
        {
            // Handle unhandled exceptions in Windows Forms
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            Application.ThreadException += (sender, e) =>
            {
                HandleError(e.Exception, "An unhandled error occurred in the application");
            };

            // Handle unhandled exceptions in other threads
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                var exception = e.ExceptionObject as Exception;
                if (exception != null)
                {
                    _logger.LogFatal("Unhandled exception in application domain", exception);
                    
                    if (e.IsTerminating)
                    {
                        _logger.LogFatal("Application is terminating due to unhandled exception");
                    }
                }
            };
        }
    }
}
