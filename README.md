# IIS Log Analyzer

A Windows desktop application for analyzing IIS log files and providing comprehensive API-wise results and insights.

## Features

### 🔍 **Advanced Log Parsing**
- Parse IIS log files in W3C Extended Log Format
- Support for single file or multiple file analysis
- Batch processing of entire directories with memory optimization
- Real-time progress tracking during parsing
- Configurable batch processing for large files
- Automatic field detection and validation

### 📊 **Comprehensive API Analysis**
- **Request Metrics**: Total requests, success/error rates, response times
- **Performance Analysis**: Average, minimum, and maximum response times
- **Traffic Analysis**: Requests per hour, unique client tracking
- **Status Code Distribution**: Detailed breakdown of HTTP status codes
- **Data Transfer**: Bytes sent/received analysis
- **Time-based Analysis**: Traffic patterns over time

### 🎯 **Key Metrics Provided**
- **Success Rate**: Percentage of successful requests (2xx status codes)
- **Error Rate**: Percentage of failed requests (4xx/5xx status codes)
- **Average Response Time**: Mean response time in milliseconds
- **Unique Clients**: Number of distinct IP addresses
- **Requests Per Hour**: Traffic volume analysis
- **Top APIs**: Most requested endpoints
- **Slowest APIs**: Endpoints with highest response times
- **Error-Prone APIs**: Endpoints with highest error rates

### 🔧 **Advanced Filtering & Search**
- **Basic Text Filtering**: Quick search by API endpoint or HTTP method
- **Advanced Filter Dialog**: Complex filtering with multiple criteria
- **Date Range Filtering**: Filter by specific time periods
- **Status Code Filtering**: Filter by HTTP status code ranges
- **Response Time Filtering**: Filter by performance thresholds
- **Volume Filtering**: Filter by minimum request counts or unique clients
- **Real-time Filter Application**: Instant results as you type

### 🖥️ **Enhanced User Interface**
- **Modern Windows Forms Design**: Clean, professional interface
- **File Selection**: Easy file/folder selection with remembered directories
- **Tabbed Data Visualization**: Organized display of different metrics
- **Sortable Grids**: Click column headers to sort results
- **Progress Tracking**: Visual progress bar with detailed status updates
- **Summary Dashboard**: Quick overview of analysis results
- **Color-coded Results**: Visual indicators for performance and errors
- **Configurable UI**: Customizable colors and thresholds

### 📊 **Data Visualization**
- **Top APIs Display**: Ranked list of most active endpoints
- **Status Code Distribution**: Visual breakdown of HTTP responses
- **Response Time Analysis**: Performance metrics with color coding
- **HTTP Method Distribution**: Request type analysis
- **Comprehensive Summary**: Detailed text-based analysis report
- **Export Visualizations**: Save charts and data for presentations

### 📤 **Multiple Export Formats**
- **CSV Export**: Spreadsheet-compatible format for further analysis
- **JSON Export**: Machine-readable format with complete data
- **HTML Report**: Professional-looking web report with styling
- **Text Summary**: Detailed analysis summary for documentation

### ⚙️ **Configuration & Settings**
- **Analysis Settings**: Configurable thresholds and defaults
- **Performance Settings**: Memory optimization and batch processing options
- **Color Settings**: Customizable color schemes for different metrics
- **Export Settings**: Default formats and directory preferences
- **Window Settings**: Remembered window size and position
- **Auto-refresh**: Optional periodic analysis updates

### 🛡️ **Error Handling & Logging**
- **Comprehensive Error Handling**: Graceful handling of all error conditions
- **Detailed Logging**: Application logs for troubleshooting
- **User-friendly Error Messages**: Clear explanations of issues
- **Log File Access**: Easy access to application logs
- **Automatic Log Cleanup**: Maintains log files for 30 days
- **Global Exception Handling**: Prevents application crashes

## Installation

### Prerequisites
- Windows 10/11
- .NET 9.0 Runtime or later

### Building from Source
1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd IISLogger
   ```

2. Build the application:
   ```bash
   cd IISLogAnalyzer
   dotnet build
   ```

3. Run the application:
   ```bash
   dotnet run
   ```

### Creating Executable
To create a standalone executable:
```bash
dotnet publish -c Release -r win-x64 --self-contained
```

## Usage

### 1. **Load Log Files**
- Click **"Select Log Files"** to choose individual IIS log files
- Click **"Select Log Folder"** to analyze all .log files in a directory
- The application supports multiple file selection and remembers your last directory
- Large files are processed efficiently with progress tracking

### 2. **Configure Settings (Optional)**
- Click **"Settings"** to customize analysis parameters
- Set performance thresholds, color schemes, and export preferences
- Configure memory optimization and batch processing options
- Adjust UI preferences and default values

### 3. **View Analysis Results**
- Results are displayed in a sortable grid with color-coded performance indicators
- Main grid shows:
  - API Endpoint
  - HTTP Method
  - Total Requests
  - Success Rate (color-coded)
  - Error Rate (color-coded)
  - Average Response Time
  - Unique Clients
  - Requests Per Hour

### 4. **Apply Filters**
- **Basic Filtering**: Use the filter box to search by API endpoint or HTTP method
- **Advanced Filtering**: Click "Advanced Filter" for complex criteria:
  - Date range filtering
  - Status code filtering
  - Response time thresholds
  - Volume-based filtering
- **Clear Filters**: Reset all filters with one click
- Results update in real-time as you apply filters

### 5. **Visualize Data**
- Click **"Show Charts"** to open the data visualization window
- View tabbed displays of:
  - Top APIs by request volume
  - Status code distribution
  - Response time analysis
  - HTTP method distribution
  - Comprehensive text summary
- Export visualization data for presentations

### 6. **Export Results**
- **CSV**: For Excel/spreadsheet analysis
- **JSON**: For programmatic processing with complete data
- **HTML**: For sharing professional reports with stakeholders
- **Text Summary**: For documentation and analysis reports

### 7. **Access Help & Logs**
- Use the **Help** menu to:
  - View application logs for troubleshooting
  - Access the About dialog with version information
- Logs are automatically maintained and cleaned up

## Sample Data

The application includes sample IIS log data in `SampleData/sample_iis.log` for testing purposes.

## IIS Log Format Support

The application supports the standard IIS W3C Extended Log Format with the following fields:
- `date` - Date of the request
- `time` - Time of the request
- `s-ip` - Server IP address
- `cs-method` - HTTP method (GET, POST, PUT, DELETE, etc.)
- `cs-uri-stem` - URI stem (API endpoint)
- `cs-uri-query` - Query string parameters
- `s-port` - Server port
- `cs-username` - Username (if authenticated)
- `c-ip` - Client IP address
- `cs(User-Agent)` - User agent string
- `cs(Referer)` - Referer header
- `sc-status` - HTTP status code
- `sc-substatus` - Sub-status code
- `sc-win32-status` - Win32 status code
- `sc-bytes` - Bytes sent to client
- `cs-bytes` - Bytes received from client
- `time-taken` - Time taken to process request (milliseconds)

## Architecture

### Core Components

1. **IISLogParser**: Advanced parser for W3C Extended Log Format files with progress tracking
2. **ApiAnalysisService**: Comprehensive analysis engine with filtering and metrics generation
3. **ExportService**: Multi-format export handler (CSV, JSON, HTML, Text)
4. **LoggingService**: Centralized logging with automatic cleanup and error tracking
5. **ErrorHandler**: Global error handling with user-friendly dialogs
6. **MainForm**: Enhanced Windows Forms UI with advanced features
7. **AdvancedFilterForm**: Complex filtering interface with multiple criteria
8. **ChartsForm**: Data visualization interface with tabbed displays
9. **SettingsForm**: Configuration interface for all application settings

### Data Models

- **IISLogEntry**: Represents a single log entry with computed properties
- **ApiAnalysisResult**: Comprehensive analysis results for API endpoints
- **OverallAnalysisSummary**: Aggregated analysis with top performers and summaries
- **FilterCriteria**: Advanced filtering parameters and matching logic
- **ApplicationSettings**: Complete configuration management with persistence

### Services & Utilities

- **LoggingService**: Singleton logging service with multiple log levels
- **ErrorHandler**: Static utility for consistent error handling
- **Settings Management**: JSON-based configuration with automatic migration

## Performance

- **Memory Efficient**: Streams large log files without loading everything into memory
- **Progress Tracking**: Real-time progress updates during processing
- **Responsive UI**: Non-blocking operations with async/await patterns
- **Fast Filtering**: Client-side filtering for instant results

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues, questions, or feature requests, please create an issue in the GitHub repository.
