using System;
using System.Collections.Generic;
using System.Linq;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Services
{
    /// <summary>
    /// Service for performing detailed performance analysis on IIS log data
    /// </summary>
    public class PerformanceAnalysisService
    {
        /// <summary>
        /// Perform comprehensive performance analysis
        /// </summary>
        public PerformanceAnalysis AnalyzePerformance(List<IISLogEntry> logEntries)
        {
            if (!logEntries.Any())
                return new PerformanceAnalysis();

            var analysis = new PerformanceAnalysis
            {
                TrafficAnalysis = AnalyzeTrafficPatterns(logEntries),
                ResponseTimeAnalysis = AnalyzeResponseTimes(logEntries),
                BandwidthAnalysis = AnalyzeBandwidth(logEntries),
                SlowestEndpoints = AnalyzeSlowestEndpoints(logEntries)
            };

            return analysis;
        }

        /// <summary>
        /// Analyze traffic patterns and peak hours
        /// </summary>
        private TrafficAnalysis AnalyzeTrafficPatterns(List<IISLogEntry> logEntries)
        {
            var analysis = new TrafficAnalysis();

            // Group by hour
            var hourlyGroups = logEntries
                .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, 0, 0))
                .ToDictionary(g => g.Key, g => g.Count());

            analysis.HourlyTraffic = hourlyGroups;

            // Group by minute for more granular analysis
            var minutelyGroups = logEntries
                .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, e.DateTime.Minute, 0))
                .ToDictionary(g => g.Key, g => g.Count());

            analysis.MinutelyTraffic = minutelyGroups;

            // Find peak hour
            if (hourlyGroups.Any())
            {
                var peakHour = hourlyGroups.OrderByDescending(kvp => kvp.Value).First();
                analysis.PeakHour = new PeakTrafficInfo
                {
                    TimeStamp = peakHour.Key,
                    RequestCount = peakHour.Value,
                    PercentageOfTotal = (double)peakHour.Value / logEntries.Count * 100
                };

                analysis.AverageRequestsPerHour = hourlyGroups.Values.Average();
            }

            // Find peak minute
            if (minutelyGroups.Any())
            {
                var peakMinute = minutelyGroups.OrderByDescending(kvp => kvp.Value).First();
                analysis.PeakMinute = new PeakTrafficInfo
                {
                    TimeStamp = peakMinute.Key,
                    RequestCount = peakMinute.Value,
                    PercentageOfTotal = (double)peakMinute.Value / logEntries.Count * 100
                };

                analysis.AverageRequestsPerMinute = minutelyGroups.Values.Average();
            }

            return analysis;
        }

        /// <summary>
        /// Analyze response time percentiles
        /// </summary>
        private ResponseTimeAnalysis AnalyzeResponseTimes(List<IISLogEntry> logEntries)
        {
            var analysis = new ResponseTimeAnalysis();
            var responseTimes = logEntries.Select(e => (double)e.TimeTaken).OrderBy(t => t).ToList();

            if (!responseTimes.Any())
                return analysis;

            // Calculate percentiles
            analysis.P50 = CalculatePercentile(responseTimes, 50);
            analysis.P90 = CalculatePercentile(responseTimes, 90);
            analysis.P95 = CalculatePercentile(responseTimes, 95);
            analysis.P99 = CalculatePercentile(responseTimes, 99);
            analysis.Average = responseTimes.Average();
            analysis.Minimum = (int)responseTimes.Min();
            analysis.Maximum = (int)responseTimes.Max();
            analysis.StandardDeviation = CalculateStandardDeviation(responseTimes);

            // Calculate percentiles by endpoint
            var endpointGroups = logEntries.GroupBy(e => $"{e.Method} {e.ApiEndpoint}");
            foreach (var group in endpointGroups.Where(g => g.Count() >= 10)) // Only endpoints with sufficient data
            {
                var endpointTimes = group.Select(e => (double)e.TimeTaken).OrderBy(t => t).ToList();
                var parts = group.Key.Split(' ', 2);
                
                analysis.ByEndpoint[group.Key] = new ResponseTimePercentiles
                {
                    Endpoint = parts.Length > 1 ? parts[1] : group.Key,
                    Method = parts.Length > 1 ? parts[0] : "GET",
                    P50 = CalculatePercentile(endpointTimes, 50),
                    P90 = CalculatePercentile(endpointTimes, 90),
                    P95 = CalculatePercentile(endpointTimes, 95),
                    P99 = CalculatePercentile(endpointTimes, 99),
                    Average = endpointTimes.Average(),
                    SampleCount = endpointTimes.Count
                };
            }

            return analysis;
        }

        /// <summary>
        /// Analyze bandwidth usage patterns
        /// </summary>
        private BandwidthAnalysis AnalyzeBandwidth(List<IISLogEntry> logEntries)
        {
            var analysis = new BandwidthAnalysis();

            // Group by hour for bandwidth analysis
            var hourlyBytesSent = logEntries
                .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, 0, 0))
                .ToDictionary(g => g.Key, g => g.Sum(e => e.BytesSent));

            var hourlyBytesReceived = logEntries
                .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, 0, 0))
                .ToDictionary(g => g.Key, g => g.Sum(e => e.BytesReceived));

            analysis.HourlyBytesSent = hourlyBytesSent;
            analysis.HourlyBytesReceived = hourlyBytesReceived;
            analysis.TotalBytesSent = logEntries.Sum(e => e.BytesSent);
            analysis.TotalBytesReceived = logEntries.Sum(e => e.BytesReceived);
            analysis.AverageBytesPerRequest = logEntries.Any() ? 
                (double)(analysis.TotalBytesSent + analysis.TotalBytesReceived) / logEntries.Count : 0;

            // Find peak bandwidth hours
            if (hourlyBytesSent.Any())
            {
                var peakSent = hourlyBytesSent.OrderByDescending(kvp => kvp.Value).First();
                analysis.PeakSentHour = new PeakBandwidthInfo
                {
                    TimeStamp = peakSent.Key,
                    Bytes = peakSent.Value,
                    FormattedSize = FormatBytes(peakSent.Value)
                };
            }

            if (hourlyBytesReceived.Any())
            {
                var peakReceived = hourlyBytesReceived.OrderByDescending(kvp => kvp.Value).First();
                analysis.PeakReceivedHour = new PeakBandwidthInfo
                {
                    TimeStamp = peakReceived.Key,
                    Bytes = peakReceived.Value,
                    FormattedSize = FormatBytes(peakReceived.Value)
                };
            }

            // Analyze bandwidth by endpoint
            var endpointBandwidth = logEntries
                .GroupBy(e => new { e.Method, e.ApiEndpoint })
                .Select(g => new EndpointBandwidth
                {
                    Endpoint = g.Key.ApiEndpoint,
                    Method = g.Key.Method,
                    TotalBytesSent = g.Sum(e => e.BytesSent),
                    TotalBytesReceived = g.Sum(e => e.BytesReceived),
                    RequestCount = g.Count(),
                    AverageBytesPerRequest = g.Any() ? (double)(g.Sum(e => e.BytesSent + e.BytesReceived)) / g.Count() : 0
                })
                .OrderByDescending(e => e.TotalBytesSent + e.TotalBytesReceived)
                .Take(20)
                .ToList();

            foreach (var endpoint in endpointBandwidth)
            {
                endpoint.FormattedTotalSent = FormatBytes(endpoint.TotalBytesSent);
                endpoint.FormattedTotalReceived = FormatBytes(endpoint.TotalBytesReceived);
            }

            analysis.TopBandwidthConsumers = endpointBandwidth;

            return analysis;
        }

        /// <summary>
        /// Analyze slowest endpoints with detailed breakdowns
        /// </summary>
        private List<EndpointPerformance> AnalyzeSlowestEndpoints(List<IISLogEntry> logEntries)
        {
            var endpointGroups = logEntries
                .GroupBy(e => new { e.Method, e.ApiEndpoint })
                .Where(g => g.Count() >= 5) // Only endpoints with sufficient data
                .Select(g => new EndpointPerformance
                {
                    Endpoint = g.Key.ApiEndpoint,
                    Method = g.Key.Method,
                    TotalRequests = g.Count(),
                    AverageResponseTime = g.Average(e => e.TimeTaken),
                    MinResponseTime = g.Min(e => e.TimeTaken),
                    MaxResponseTime = g.Max(e => e.TimeTaken),
                    ErrorRate = (double)g.Count(e => !e.IsSuccessful) / g.Count() * 100,
                    TotalBandwidth = g.Sum(e => e.BytesSent + e.BytesReceived)
                })
                .OrderByDescending(e => e.AverageResponseTime)
                .Take(20)
                .ToList();

            // Add detailed analysis for each endpoint
            foreach (var endpoint in endpointGroups)
            {
                var endpointEntries = logEntries
                    .Where(e => e.Method == endpoint.Method && e.ApiEndpoint == endpoint.Endpoint)
                    .ToList();

                var responseTimes = endpointEntries.Select(e => (double)e.TimeTaken).OrderBy(t => t).ToList();
                
                endpoint.Percentiles = new ResponseTimePercentiles
                {
                    Endpoint = endpoint.Endpoint,
                    Method = endpoint.Method,
                    P50 = CalculatePercentile(responseTimes, 50),
                    P90 = CalculatePercentile(responseTimes, 90),
                    P95 = CalculatePercentile(responseTimes, 95),
                    P99 = CalculatePercentile(responseTimes, 99),
                    Average = responseTimes.Average(),
                    SampleCount = responseTimes.Count
                };

                // Get slowest individual requests
                endpoint.SlowestRequests = endpointEntries
                    .OrderByDescending(e => e.TimeTaken)
                    .Take(5)
                    .Select(e => new SlowRequest
                    {
                        TimeStamp = e.DateTime,
                        ClientIP = e.ClientIP,
                        ResponseTime = e.TimeTaken,
                        StatusCode = e.StatusCode,
                        BytesSent = e.BytesSent,
                        UserAgent = e.UserAgent,
                        FullUri = e.FullUri
                    })
                    .ToList();

                // Response time distribution
                endpoint.ResponseTimeDistribution = CreateResponseTimeDistribution(responseTimes);
            }

            return endpointGroups;
        }

        /// <summary>
        /// Calculate percentile value from sorted list
        /// </summary>
        private double CalculatePercentile(List<double> sortedValues, double percentile)
        {
            if (!sortedValues.Any()) return 0;
            
            double index = (percentile / 100.0) * (sortedValues.Count - 1);
            int lowerIndex = (int)Math.Floor(index);
            int upperIndex = (int)Math.Ceiling(index);
            
            if (lowerIndex == upperIndex)
                return sortedValues[lowerIndex];
            
            double weight = index - lowerIndex;
            return sortedValues[lowerIndex] * (1 - weight) + sortedValues[upperIndex] * weight;
        }

        /// <summary>
        /// Calculate standard deviation
        /// </summary>
        private double CalculateStandardDeviation(List<double> values)
        {
            if (values.Count <= 1) return 0;
            
            double mean = values.Average();
            double sumOfSquaredDifferences = values.Sum(val => Math.Pow(val - mean, 2));
            return Math.Sqrt(sumOfSquaredDifferences / (values.Count - 1));
        }

        /// <summary>
        /// Create response time distribution buckets
        /// </summary>
        private Dictionary<int, int> CreateResponseTimeDistribution(List<double> responseTimes)
        {
            var distribution = new Dictionary<int, int>();
            var buckets = new[] { 100, 250, 500, 1000, 2000, 5000, 10000, int.MaxValue };
            var bucketLabels = new[] { 100, 250, 500, 1000, 2000, 5000, 10000, 999999 };

            for (int i = 0; i < buckets.Length; i++)
            {
                int count = responseTimes.Count(t => 
                    (i == 0 ? t <= buckets[i] : t > buckets[i - 1] && t <= buckets[i]));
                distribution[bucketLabels[i]] = count;
            }

            return distribution;
        }

        /// <summary>
        /// Format bytes to human readable format
        /// </summary>
        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            
            return $"{number:n1} {suffixes[counter]}";
        }
    }
}
