﻿namespace IISLogAnalyzer
{
    partial class MainForm
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.btnSelectFiles = new Button();
            this.btnSelectFolder = new Button();
            this.dataGridViewResults = new DataGridView();
            this.progressBar = new ProgressBar();
            this.lblStatus = new Label();
            this.lblTotalEntries = new Label();
            this.lblTotalApis = new Label();
            this.lblTimeSpan = new Label();
            this.btnExportCsv = new Button();
            this.btnExportJson = new Button();
            this.btnExportHtml = new Button();
            this.btnShowCharts = new Button();
            this.txtFilter = new TextBox();
            this.lblFilter = new Label();
            this.btnAdvancedFilter = new Button();
            this.btnClearFilters = new Button();
            this.lblFilterStatus = new Label();
            this.btnSettings = new Button();
            this.menuStrip = new MenuStrip();
            this.helpToolStripMenuItem = new ToolStripMenuItem();
            this.viewLogToolStripMenuItem = new ToolStripMenuItem();
            this.aboutToolStripMenuItem = new ToolStripMenuItem();
            this.panelTop = new Panel();
            this.panelBottom = new Panel();
            this.panelMain = new Panel();
            this.groupBoxSummary = new GroupBox();
            this.groupBoxExport = new GroupBox();

            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewResults)).BeginInit();
            this.panelTop.SuspendLayout();
            this.panelBottom.SuspendLayout();
            this.panelMain.SuspendLayout();
            this.groupBoxSummary.SuspendLayout();
            this.groupBoxExport.SuspendLayout();
            this.menuStrip.SuspendLayout();
            this.SuspendLayout();

            //
            // menuStrip
            //
            this.menuStrip.Items.AddRange(new ToolStripItem[] { this.helpToolStripMenuItem });
            this.menuStrip.Location = new Point(0, 0);
            this.menuStrip.Name = "menuStrip";
            this.menuStrip.Size = new Size(1200, 24);
            this.menuStrip.TabIndex = 0;
            this.menuStrip.Text = "menuStrip";

            //
            // helpToolStripMenuItem
            //
            this.helpToolStripMenuItem.DropDownItems.AddRange(new ToolStripItem[] {
                this.viewLogToolStripMenuItem,
                this.aboutToolStripMenuItem });
            this.helpToolStripMenuItem.Name = "helpToolStripMenuItem";
            this.helpToolStripMenuItem.Size = new Size(44, 20);
            this.helpToolStripMenuItem.Text = "Help";

            //
            // viewLogToolStripMenuItem
            //
            this.viewLogToolStripMenuItem.Name = "viewLogToolStripMenuItem";
            this.viewLogToolStripMenuItem.Size = new Size(125, 22);
            this.viewLogToolStripMenuItem.Text = "View Log";
            this.viewLogToolStripMenuItem.Click += new EventHandler(this.viewLogToolStripMenuItem_Click);

            //
            // aboutToolStripMenuItem
            //
            this.aboutToolStripMenuItem.Name = "aboutToolStripMenuItem";
            this.aboutToolStripMenuItem.Size = new Size(125, 22);
            this.aboutToolStripMenuItem.Text = "About";
            this.aboutToolStripMenuItem.Click += new EventHandler(this.aboutToolStripMenuItem_Click);

            //
            // panelTop
            //
            this.panelTop.Controls.Add(this.btnSelectFiles);
            this.panelTop.Controls.Add(this.btnSelectFolder);
            this.panelTop.Controls.Add(this.lblFilter);
            this.panelTop.Controls.Add(this.txtFilter);
            this.panelTop.Controls.Add(this.btnAdvancedFilter);
            this.panelTop.Controls.Add(this.btnClearFilters);
            this.panelTop.Controls.Add(this.lblFilterStatus);
            this.panelTop.Controls.Add(this.btnSettings);
            this.panelTop.Dock = DockStyle.Top;
            this.panelTop.Location = new Point(0, 0);
            this.panelTop.Name = "panelTop";
            this.panelTop.Size = new Size(1200, 110);
            this.panelTop.TabIndex = 0;

            //
            // btnSelectFiles
            //
            this.btnSelectFiles.Location = new Point(12, 12);
            this.btnSelectFiles.Name = "btnSelectFiles";
            this.btnSelectFiles.Size = new Size(120, 30);
            this.btnSelectFiles.TabIndex = 0;
            this.btnSelectFiles.Text = "Select Log Files";
            this.btnSelectFiles.UseVisualStyleBackColor = true;
            this.btnSelectFiles.Click += new EventHandler(this.btnSelectFiles_Click);

            //
            // btnSelectFolder
            //
            this.btnSelectFolder.Location = new Point(138, 12);
            this.btnSelectFolder.Name = "btnSelectFolder";
            this.btnSelectFolder.Size = new Size(120, 30);
            this.btnSelectFolder.TabIndex = 1;
            this.btnSelectFolder.Text = "Select Log Folder";
            this.btnSelectFolder.UseVisualStyleBackColor = true;
            this.btnSelectFolder.Click += new EventHandler(this.btnSelectFolder_Click);

            //
            // lblFilter
            //
            this.lblFilter.AutoSize = true;
            this.lblFilter.Location = new Point(12, 52);
            this.lblFilter.Name = "lblFilter";
            this.lblFilter.Size = new Size(37, 15);
            this.lblFilter.TabIndex = 2;
            this.lblFilter.Text = "Filter:";

            //
            // txtFilter
            //
            this.txtFilter.Location = new Point(55, 49);
            this.txtFilter.Name = "txtFilter";
            this.txtFilter.PlaceholderText = "Filter by API endpoint or method...";
            this.txtFilter.Size = new Size(300, 23);
            this.txtFilter.TabIndex = 3;
            this.txtFilter.TextChanged += new EventHandler(this.txtFilter_TextChanged);

            //
            // btnAdvancedFilter
            //
            this.btnAdvancedFilter.Location = new Point(365, 49);
            this.btnAdvancedFilter.Name = "btnAdvancedFilter";
            this.btnAdvancedFilter.Size = new Size(100, 23);
            this.btnAdvancedFilter.TabIndex = 4;
            this.btnAdvancedFilter.Text = "Advanced Filter";
            this.btnAdvancedFilter.UseVisualStyleBackColor = true;
            this.btnAdvancedFilter.Click += new EventHandler(this.btnAdvancedFilter_Click);

            //
            // btnClearFilters
            //
            this.btnClearFilters.Enabled = false;
            this.btnClearFilters.Location = new Point(471, 49);
            this.btnClearFilters.Name = "btnClearFilters";
            this.btnClearFilters.Size = new Size(80, 23);
            this.btnClearFilters.TabIndex = 5;
            this.btnClearFilters.Text = "Clear Filters";
            this.btnClearFilters.UseVisualStyleBackColor = true;
            this.btnClearFilters.Click += new EventHandler(this.btnClearFilters_Click);

            //
            // lblFilterStatus
            //
            this.lblFilterStatus.AutoSize = true;
            this.lblFilterStatus.ForeColor = Color.Gray;
            this.lblFilterStatus.Location = new Point(12, 82);
            this.lblFilterStatus.Name = "lblFilterStatus";
            this.lblFilterStatus.Size = new Size(100, 15);
            this.lblFilterStatus.TabIndex = 6;
            this.lblFilterStatus.Text = "No filters applied";

            //
            // btnSettings
            //
            this.btnSettings.Location = new Point(557, 49);
            this.btnSettings.Name = "btnSettings";
            this.btnSettings.Size = new Size(70, 23);
            this.btnSettings.TabIndex = 7;
            this.btnSettings.Text = "Settings";
            this.btnSettings.UseVisualStyleBackColor = true;
            this.btnSettings.Click += new EventHandler(this.btnSettings_Click);

            //
            // panelMain
            //
            this.panelMain.Controls.Add(this.dataGridViewResults);
            this.panelMain.Controls.Add(this.groupBoxSummary);
            this.panelMain.Dock = DockStyle.Fill;
            this.panelMain.Location = new Point(0, 134);
            this.panelMain.Name = "panelMain";
            this.panelMain.Size = new Size(1200, 566);
            this.panelMain.TabIndex = 1;

            //
            // groupBoxSummary
            //
            this.groupBoxSummary.Controls.Add(this.lblTotalEntries);
            this.groupBoxSummary.Controls.Add(this.lblTotalApis);
            this.groupBoxSummary.Controls.Add(this.lblTimeSpan);
            this.groupBoxSummary.Controls.Add(this.groupBoxExport);
            this.groupBoxSummary.Dock = DockStyle.Top;
            this.groupBoxSummary.Location = new Point(0, 0);
            this.groupBoxSummary.Name = "groupBoxSummary";
            this.groupBoxSummary.Size = new Size(1200, 80);
            this.groupBoxSummary.TabIndex = 0;
            this.groupBoxSummary.TabStop = false;
            this.groupBoxSummary.Text = "Analysis Summary";

            //
            // lblTotalEntries
            //
            this.lblTotalEntries.AutoSize = true;
            this.lblTotalEntries.Location = new Point(12, 25);
            this.lblTotalEntries.Name = "lblTotalEntries";
            this.lblTotalEntries.Size = new Size(80, 15);
            this.lblTotalEntries.TabIndex = 0;
            this.lblTotalEntries.Text = "Total Entries: 0";

            //
            // lblTotalApis
            //
            this.lblTotalApis.AutoSize = true;
            this.lblTotalApis.Location = new Point(12, 45);
            this.lblTotalApis.Name = "lblTotalApis";
            this.lblTotalApis.Size = new Size(70, 15);
            this.lblTotalApis.TabIndex = 1;
            this.lblTotalApis.Text = "Total APIs: 0";

            //
            // lblTimeSpan
            //
            this.lblTimeSpan.AutoSize = true;
            this.lblTimeSpan.Location = new Point(200, 25);
            this.lblTimeSpan.Name = "lblTimeSpan";
            this.lblTimeSpan.Size = new Size(80, 15);
            this.lblTimeSpan.TabIndex = 2;
            this.lblTimeSpan.Text = "Time Span: 0h";

            //
            // groupBoxExport
            //
            this.groupBoxExport.Controls.Add(this.btnExportCsv);
            this.groupBoxExport.Controls.Add(this.btnExportJson);
            this.groupBoxExport.Controls.Add(this.btnExportHtml);
            this.groupBoxExport.Controls.Add(this.btnShowCharts);
            this.groupBoxExport.Location = new Point(400, 15);
            this.groupBoxExport.Name = "groupBoxExport";
            this.groupBoxExport.Size = new Size(450, 55);
            this.groupBoxExport.TabIndex = 3;
            this.groupBoxExport.TabStop = false;
            this.groupBoxExport.Text = "Export Results & Visualizations";

            //
            // btnExportCsv
            //
            this.btnExportCsv.Enabled = false;
            this.btnExportCsv.Location = new Point(10, 20);
            this.btnExportCsv.Name = "btnExportCsv";
            this.btnExportCsv.Size = new Size(80, 25);
            this.btnExportCsv.TabIndex = 0;
            this.btnExportCsv.Text = "Export CSV";
            this.btnExportCsv.UseVisualStyleBackColor = true;
            this.btnExportCsv.Click += new EventHandler(this.btnExportCsv_Click);

            //
            // btnExportJson
            //
            this.btnExportJson.Enabled = false;
            this.btnExportJson.Location = new Point(100, 20);
            this.btnExportJson.Name = "btnExportJson";
            this.btnExportJson.Size = new Size(80, 25);
            this.btnExportJson.TabIndex = 1;
            this.btnExportJson.Text = "Export JSON";
            this.btnExportJson.UseVisualStyleBackColor = true;
            this.btnExportJson.Click += new EventHandler(this.btnExportJson_Click);

            //
            // btnExportHtml
            //
            this.btnExportHtml.Enabled = false;
            this.btnExportHtml.Location = new Point(190, 20);
            this.btnExportHtml.Name = "btnExportHtml";
            this.btnExportHtml.Size = new Size(80, 25);
            this.btnExportHtml.TabIndex = 2;
            this.btnExportHtml.Text = "Export HTML";
            this.btnExportHtml.UseVisualStyleBackColor = true;
            this.btnExportHtml.Click += new EventHandler(this.btnExportHtml_Click);

            //
            // btnShowCharts
            //
            this.btnShowCharts.Enabled = false;
            this.btnShowCharts.Location = new Point(280, 20);
            this.btnShowCharts.Name = "btnShowCharts";
            this.btnShowCharts.Size = new Size(80, 25);
            this.btnShowCharts.TabIndex = 3;
            this.btnShowCharts.Text = "Show Charts";
            this.btnShowCharts.UseVisualStyleBackColor = true;
            this.btnShowCharts.Click += new EventHandler(this.btnShowCharts_Click);

            //
            // dataGridViewResults
            //
            this.dataGridViewResults.AllowUserToAddRows = false;
            this.dataGridViewResults.AllowUserToDeleteRows = false;
            this.dataGridViewResults.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridViewResults.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewResults.Dock = DockStyle.Fill;
            this.dataGridViewResults.Location = new Point(0, 80);
            this.dataGridViewResults.Name = "dataGridViewResults";
            this.dataGridViewResults.ReadOnly = true;
            this.dataGridViewResults.RowHeadersVisible = false;
            this.dataGridViewResults.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewResults.Size = new Size(1200, 540);
            this.dataGridViewResults.TabIndex = 1;

            // Initialize columns
            this.dataGridViewResults.Columns.Add("ApiEndpoint", "API Endpoint");
            this.dataGridViewResults.Columns.Add("Method", "Method");
            this.dataGridViewResults.Columns.Add("TotalRequests", "Total Requests");
            this.dataGridViewResults.Columns.Add("SuccessRate", "Success Rate");
            this.dataGridViewResults.Columns.Add("ErrorRate", "Error Rate");
            this.dataGridViewResults.Columns.Add("AvgResponseTime", "Avg Response Time");
            this.dataGridViewResults.Columns.Add("UniqueClients", "Unique Clients");
            this.dataGridViewResults.Columns.Add("RequestsPerHour", "Requests/Hour");

            // Set column widths
            this.dataGridViewResults.Columns["ApiEndpoint"].FillWeight = 30;
            this.dataGridViewResults.Columns["Method"].FillWeight = 10;
            this.dataGridViewResults.Columns["TotalRequests"].FillWeight = 15;
            this.dataGridViewResults.Columns["SuccessRate"].FillWeight = 12;
            this.dataGridViewResults.Columns["ErrorRate"].FillWeight = 12;
            this.dataGridViewResults.Columns["AvgResponseTime"].FillWeight = 15;
            this.dataGridViewResults.Columns["UniqueClients"].FillWeight = 12;
            this.dataGridViewResults.Columns["RequestsPerHour"].FillWeight = 14;

            //
            // panelBottom
            //
            this.panelBottom.Controls.Add(this.progressBar);
            this.panelBottom.Controls.Add(this.lblStatus);
            this.panelBottom.Dock = DockStyle.Bottom;
            this.panelBottom.Location = new Point(0, 700);
            this.panelBottom.Name = "panelBottom";
            this.panelBottom.Size = new Size(1200, 50);
            this.panelBottom.TabIndex = 2;

            //
            // progressBar
            //
            this.progressBar.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
            this.progressBar.Location = new Point(12, 25);
            this.progressBar.Name = "progressBar";
            this.progressBar.Size = new Size(1176, 15);
            this.progressBar.TabIndex = 1;

            //
            // lblStatus
            //
            this.lblStatus.AutoSize = true;
            this.lblStatus.Location = new Point(12, 7);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new Size(39, 15);
            this.lblStatus.TabIndex = 0;
            this.lblStatus.Text = "Ready";

            //
            // MainForm
            //
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 750);
            this.Controls.Add(this.panelMain);
            this.Controls.Add(this.panelTop);
            this.Controls.Add(this.panelBottom);
            this.Controls.Add(this.menuStrip);
            this.MainMenuStrip = this.menuStrip;
            this.Name = "MainForm";
            this.Text = "IIS Log Analyzer";

            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewResults)).EndInit();
            this.panelTop.ResumeLayout(false);
            this.panelTop.PerformLayout();
            this.panelBottom.ResumeLayout(false);
            this.panelBottom.PerformLayout();
            this.panelMain.ResumeLayout(false);
            this.groupBoxSummary.ResumeLayout(false);
            this.groupBoxSummary.PerformLayout();
            this.groupBoxExport.ResumeLayout(false);
            this.menuStrip.ResumeLayout(false);
            this.menuStrip.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        #endregion

        private Button btnSelectFiles;
        private Button btnSelectFolder;
        private DataGridView dataGridViewResults;
        private ProgressBar progressBar;
        private Label lblStatus;
        private Label lblTotalEntries;
        private Label lblTotalApis;
        private Label lblTimeSpan;
        private Button btnExportCsv;
        private Button btnExportJson;
        private Button btnExportHtml;
        private Button btnShowCharts;
        private TextBox txtFilter;
        private Label lblFilter;
        private Button btnAdvancedFilter;
        private Button btnClearFilters;
        private Label lblFilterStatus;
        private Button btnSettings;
        private MenuStrip menuStrip;
        private ToolStripMenuItem helpToolStripMenuItem;
        private ToolStripMenuItem viewLogToolStripMenuItem;
        private ToolStripMenuItem aboutToolStripMenuItem;
        private Panel panelTop;
        private Panel panelBottom;
        private Panel panelMain;
        private GroupBox groupBoxSummary;
        private GroupBox groupBoxExport;
    }
}
