using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Services
{
    /// <summary>
    /// Service for performing security analysis on IIS log data
    /// </summary>
    public class SecurityAnalysisService
    {
        private readonly Dictionary<string, BotInfo> _knownBots;
        private readonly List<string> _attackPatterns;

        public SecurityAnalysisService()
        {
            _knownBots = InitializeKnownBots();
            _attackPatterns = InitializeAttackPatterns();
        }

        /// <summary>
        /// Perform comprehensive security analysis
        /// </summary>
        public SecurityAnalysis AnalyzeSecurity(List<IISLogEntry> logEntries)
        {
            if (!logEntries.Any())
                return new SecurityAnalysis();

            var analysis = new SecurityAnalysis
            {
                AuthenticationAnalysis = AnalyzeAuthentication(logEntries),
                SuspiciousIPs = AnalyzeSuspiciousIPs(logEntries),
                BotAnalysis = AnalyzeBots(logEntries),
                PotentialAttacks = DetectAttackPatterns(logEntries)
            };

            analysis.SecurityMetrics = CalculateSecurityMetrics(analysis, logEntries.Count);

            return analysis;
        }

        /// <summary>
        /// Analyze authentication failures
        /// </summary>
        private AuthenticationAnalysis AnalyzeAuthentication(List<IISLogEntry> logEntries)
        {
            var authFailures = logEntries.Where(e => e.StatusCode == 401 || e.StatusCode == 403).ToList();
            
            var analysis = new AuthenticationAnalysis
            {
                TotalAuthFailures = authFailures.Count,
                Unauthorized401Count = authFailures.Count(e => e.StatusCode == 401),
                Forbidden403Count = authFailures.Count(e => e.StatusCode == 403),
                FailureRate = logEntries.Any() ? (double)authFailures.Count / logEntries.Count * 100 : 0
            };

            // Failures by IP
            analysis.FailuresByIP = authFailures
                .GroupBy(e => e.ClientIP)
                .ToDictionary(g => g.Key, g => g.Count());

            // Failures by endpoint
            analysis.FailuresByEndpoint = authFailures
                .GroupBy(e => e.ApiEndpoint)
                .ToDictionary(g => g.Key, g => g.Count());

            // Failures over time (hourly)
            analysis.FailuresOverTime = authFailures
                .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, 0, 0))
                .ToDictionary(g => g.Key, g => g.Count());

            // Recent failures
            analysis.RecentFailures = authFailures
                .OrderByDescending(e => e.DateTime)
                .Take(50)
                .Select(e => new AuthFailureEvent
                {
                    TimeStamp = e.DateTime,
                    ClientIP = e.ClientIP,
                    Endpoint = e.ApiEndpoint,
                    Method = e.Method,
                    StatusCode = e.StatusCode,
                    UserAgent = e.UserAgent,
                    Referer = e.Referer
                })
                .ToList();

            return analysis;
        }

        /// <summary>
        /// Analyze suspicious IP addresses
        /// </summary>
        private List<SuspiciousIP> AnalyzeSuspiciousIPs(List<IISLogEntry> logEntries)
        {
            var ipGroups = logEntries.GroupBy(e => e.ClientIP);
            var suspiciousIPs = new List<SuspiciousIP>();

            foreach (var ipGroup in ipGroups.Where(g => g.Count() >= 10)) // Only IPs with significant activity
            {
                var entries = ipGroup.ToList();
                var errorCount = entries.Count(e => !e.IsSuccessful);
                var errorRate = (double)errorCount / entries.Count * 100;
                var authFailures = entries.Count(e => e.StatusCode == 401 || e.StatusCode == 403);

                // Determine if IP is suspicious
                var activityType = DetermineActivityType(entries, errorRate, authFailures);
                var riskScore = CalculateRiskScore(entries, errorRate, authFailures);

                if (riskScore >= 30) // Threshold for suspicious activity
                {
                    suspiciousIPs.Add(new SuspiciousIP
                    {
                        IPAddress = ipGroup.Key,
                        TotalRequests = entries.Count,
                        ErrorCount = errorCount,
                        ErrorRate = errorRate,
                        AuthFailures = authFailures,
                        TargetedEndpoints = entries.Select(e => e.ApiEndpoint).Distinct().ToList(),
                        UserAgents = entries.Select(e => e.UserAgent).Distinct().ToList(),
                        FirstSeen = entries.Min(e => e.DateTime),
                        LastSeen = entries.Max(e => e.DateTime),
                        ActivityType = activityType,
                        RiskScore = riskScore
                    });
                }
            }

            return suspiciousIPs.OrderByDescending(ip => ip.RiskScore).ToList();
        }

        /// <summary>
        /// Analyze bot and crawler activity
        /// </summary>
        private BotAnalysis AnalyzeBots(List<IISLogEntry> logEntries)
        {
            var analysis = new BotAnalysis();
            var botRequests = new List<IISLogEntry>();
            var identifiedBots = new Dictionary<string, BotInfo>();

            foreach (var entry in logEntries)
            {
                var botInfo = IdentifyBot(entry.UserAgent);
                if (botInfo != null)
                {
                    botRequests.Add(entry);
                    
                    if (!identifiedBots.ContainsKey(botInfo.Name))
                    {
                        identifiedBots[botInfo.Name] = new BotInfo
                        {
                            Name = botInfo.Name,
                            UserAgent = botInfo.UserAgent,
                            Type = botInfo.Type,
                            IsLegitimate = botInfo.IsLegitimate,
                            RequestCount = 0,
                            IPAddresses = new List<string>(),
                            FirstSeen = entry.DateTime,
                            LastSeen = entry.DateTime
                        };
                    }

                    var bot = identifiedBots[botInfo.Name];
                    bot.RequestCount++;
                    if (!bot.IPAddresses.Contains(entry.ClientIP))
                        bot.IPAddresses.Add(entry.ClientIP);
                    if (entry.DateTime < bot.FirstSeen)
                        bot.FirstSeen = entry.DateTime;
                    if (entry.DateTime > bot.LastSeen)
                        bot.LastSeen = entry.DateTime;
                }
            }

            analysis.TotalBotRequests = botRequests.Count;
            analysis.BotTrafficPercentage = logEntries.Any() ? (double)botRequests.Count / logEntries.Count * 100 : 0;
            analysis.IdentifiedBots = identifiedBots;

            // Bot distribution by User-Agent
            analysis.BotsByUserAgent = botRequests
                .GroupBy(e => e.UserAgent)
                .ToDictionary(g => g.Key, g => g.Count());

            // Identify malicious bots
            analysis.MaliciousBots = identifiedBots.Values
                .Where(b => !b.IsLegitimate)
                .Select(b => b.Name)
                .ToList();

            return analysis;
        }

        /// <summary>
        /// Detect potential attack patterns
        /// </summary>
        private List<AttackPattern> DetectAttackPatterns(List<IISLogEntry> logEntries)
        {
            var attacks = new List<AttackPattern>();

            // SQL Injection detection
            attacks.AddRange(DetectSQLInjection(logEntries));

            // XSS detection
            attacks.AddRange(DetectXSSAttempts(logEntries));

            // Path traversal detection
            attacks.AddRange(DetectPathTraversal(logEntries));

            // Brute force detection
            attacks.AddRange(DetectBruteForce(logEntries));

            // Scanning activity detection
            attacks.AddRange(DetectScanning(logEntries));

            return attacks.OrderByDescending(a => a.Severity).ToList();
        }

        /// <summary>
        /// Detect SQL injection attempts
        /// </summary>
        private List<AttackPattern> DetectSQLInjection(List<IISLogEntry> logEntries)
        {
            var sqlPatterns = new[]
            {
                @"(\bunion\b.*\bselect\b)|(\bselect\b.*\bunion\b)",
                @"(\bor\b\s+\d+\s*=\s*\d+)|(\band\b\s+\d+\s*=\s*\d+)",
                @"(\bdrop\b\s+\btable\b)|(\bdelete\b\s+\bfrom\b)",
                @"(\binsert\b\s+\binto\b)|(\bupdate\b\s+\bset\b)",
                @"(\bexec\b\s*\()|(\bexecute\b\s*\()",
                @"(\bsp_\w+)|(\bxp_\w+)",
                @"(\bwaitfor\b\s+\bdelay\b)|(\bwaitfor\b\s+\btime\b)"
            };

            return DetectPatternAttacks(logEntries, sqlPatterns, AttackType.SQLInjection, "SQL Injection");
        }

        /// <summary>
        /// Detect XSS attempts
        /// </summary>
        private List<AttackPattern> DetectXSSAttempts(List<IISLogEntry> logEntries)
        {
            var xssPatterns = new[]
            {
                @"<script[^>]*>.*?</script>",
                @"javascript:",
                @"on\w+\s*=",
                @"<iframe[^>]*>",
                @"<object[^>]*>",
                @"<embed[^>]*>",
                @"<link[^>]*>",
                @"<meta[^>]*>"
            };

            return DetectPatternAttacks(logEntries, xssPatterns, AttackType.XSSAttempt, "Cross-Site Scripting (XSS)");
        }

        /// <summary>
        /// Detect path traversal attempts
        /// </summary>
        private List<AttackPattern> DetectPathTraversal(List<IISLogEntry> logEntries)
        {
            var pathPatterns = new[]
            {
                @"\.\./",
                @"\.\.\\",
                @"%2e%2e%2f",
                @"%2e%2e%5c",
                @"\.\.%2f",
                @"\.\.%5c"
            };

            return DetectPatternAttacks(logEntries, pathPatterns, AttackType.PathTraversal, "Path Traversal");
        }

        /// <summary>
        /// Generic pattern-based attack detection
        /// </summary>
        private List<AttackPattern> DetectPatternAttacks(List<IISLogEntry> logEntries, string[] patterns, AttackType attackType, string description)
        {
            var attacks = new List<AttackPattern>();
            var suspiciousEntries = new List<IISLogEntry>();

            foreach (var entry in logEntries)
            {
                var fullUri = entry.FullUri.ToLower();
                foreach (var pattern in patterns)
                {
                    if (Regex.IsMatch(fullUri, pattern, RegexOptions.IgnoreCase))
                    {
                        suspiciousEntries.Add(entry);
                        break;
                    }
                }
            }

            if (suspiciousEntries.Any())
            {
                var attack = new AttackPattern
                {
                    Type = attackType,
                    Description = description,
                    AffectedEndpoints = suspiciousEntries.Select(e => e.ApiEndpoint).Distinct().ToList(),
                    SourceIPs = suspiciousEntries.Select(e => e.ClientIP).Distinct().ToList(),
                    Severity = CalculateAttackSeverity(suspiciousEntries),
                    FirstDetected = suspiciousEntries.Min(e => e.DateTime),
                    LastDetected = suspiciousEntries.Max(e => e.DateTime),
                    OccurrenceCount = suspiciousEntries.Count,
                    Events = suspiciousEntries.Take(20).Select(e => new AttackEvent
                    {
                        TimeStamp = e.DateTime,
                        ClientIP = e.ClientIP,
                        Endpoint = e.ApiEndpoint,
                        QueryString = e.UriQuery,
                        UserAgent = e.UserAgent,
                        StatusCode = e.StatusCode,
                        DetectionReason = $"{description} pattern detected"
                    }).ToList()
                };

                attacks.Add(attack);
            }

            return attacks;
        }

        /// <summary>
        /// Detect brute force attacks
        /// </summary>
        private List<AttackPattern> DetectBruteForce(List<IISLogEntry> logEntries)
        {
            var attacks = new List<AttackPattern>();
            var authFailures = logEntries.Where(e => e.StatusCode == 401 || e.StatusCode == 403).ToList();

            // Group by IP and look for rapid authentication failures
            var ipGroups = authFailures.GroupBy(e => e.ClientIP);
            
            foreach (var ipGroup in ipGroups)
            {
                var failures = ipGroup.OrderBy(e => e.DateTime).ToList();
                if (failures.Count >= 10) // Threshold for brute force
                {
                    // Check if failures occurred within a short time window
                    var timeSpan = failures.Last().DateTime - failures.First().DateTime;
                    if (timeSpan.TotalMinutes <= 60) // Within 1 hour
                    {
                        attacks.Add(new AttackPattern
                        {
                            Type = AttackType.BruteForce,
                            Description = "Brute Force Authentication Attack",
                            AffectedEndpoints = failures.Select(e => e.ApiEndpoint).Distinct().ToList(),
                            SourceIPs = new List<string> { ipGroup.Key },
                            Severity = Math.Min(10, failures.Count / 5), // Scale severity
                            FirstDetected = failures.First().DateTime,
                            LastDetected = failures.Last().DateTime,
                            OccurrenceCount = failures.Count,
                            Events = failures.Take(10).Select(e => new AttackEvent
                            {
                                TimeStamp = e.DateTime,
                                ClientIP = e.ClientIP,
                                Endpoint = e.ApiEndpoint,
                                UserAgent = e.UserAgent,
                                StatusCode = e.StatusCode,
                                DetectionReason = "Rapid authentication failures detected"
                            }).ToList()
                        });
                    }
                }
            }

            return attacks;
        }

        /// <summary>
        /// Detect scanning activity
        /// </summary>
        private List<AttackPattern> DetectScanning(List<IISLogEntry> logEntries)
        {
            var attacks = new List<AttackPattern>();
            var ipGroups = logEntries.GroupBy(e => e.ClientIP);

            foreach (var ipGroup in ipGroups)
            {
                var entries = ipGroup.ToList();
                var uniqueEndpoints = entries.Select(e => e.ApiEndpoint).Distinct().Count();
                var errorRate = (double)entries.Count(e => !e.IsSuccessful) / entries.Count * 100;

                // Scanning indicators: many unique endpoints, high error rate, short time span
                if (uniqueEndpoints >= 20 && errorRate >= 50 && entries.Count >= 50)
                {
                    var timeSpan = entries.Max(e => e.DateTime) - entries.Min(e => e.DateTime);
                    if (timeSpan.TotalHours <= 24)
                    {
                        attacks.Add(new AttackPattern
                        {
                            Type = AttackType.Scanning,
                            Description = "Automated Scanning Activity",
                            AffectedEndpoints = entries.Select(e => e.ApiEndpoint).Distinct().Take(10).ToList(),
                            SourceIPs = new List<string> { ipGroup.Key },
                            Severity = Math.Min(10, (int)(errorRate / 10)),
                            FirstDetected = entries.Min(e => e.DateTime),
                            LastDetected = entries.Max(e => e.DateTime),
                            OccurrenceCount = entries.Count,
                            Events = entries.Where(e => !e.IsSuccessful).Take(10).Select(e => new AttackEvent
                            {
                                TimeStamp = e.DateTime,
                                ClientIP = e.ClientIP,
                                Endpoint = e.ApiEndpoint,
                                UserAgent = e.UserAgent,
                                StatusCode = e.StatusCode,
                                DetectionReason = "Scanning pattern detected"
                            }).ToList()
                        });
                    }
                }
            }

            return attacks;
        }

        /// <summary>
        /// Initialize known bot patterns
        /// </summary>
        private Dictionary<string, BotInfo> InitializeKnownBots()
        {
            return new Dictionary<string, BotInfo>
            {
                ["Googlebot"] = new BotInfo { Name = "Googlebot", Type = BotType.SearchEngine, IsLegitimate = true },
                ["Bingbot"] = new BotInfo { Name = "Bingbot", Type = BotType.SearchEngine, IsLegitimate = true },
                ["Slurp"] = new BotInfo { Name = "Yahoo Slurp", Type = BotType.SearchEngine, IsLegitimate = true },
                ["DuckDuckBot"] = new BotInfo { Name = "DuckDuckBot", Type = BotType.SearchEngine, IsLegitimate = true },
                ["facebookexternalhit"] = new BotInfo { Name = "Facebook Bot", Type = BotType.SocialMedia, IsLegitimate = true },
                ["Twitterbot"] = new BotInfo { Name = "Twitterbot", Type = BotType.SocialMedia, IsLegitimate = true },
                ["LinkedInBot"] = new BotInfo { Name = "LinkedInBot", Type = BotType.SocialMedia, IsLegitimate = true },
                ["WhatsApp"] = new BotInfo { Name = "WhatsApp", Type = BotType.SocialMedia, IsLegitimate = true },
                ["UptimeRobot"] = new BotInfo { Name = "UptimeRobot", Type = BotType.Monitoring, IsLegitimate = true },
                ["Pingdom"] = new BotInfo { Name = "Pingdom", Type = BotType.Monitoring, IsLegitimate = true },
                ["curl"] = new BotInfo { Name = "cURL", Type = BotType.Unknown, IsLegitimate = false },
                ["wget"] = new BotInfo { Name = "wget", Type = BotType.Scraper, IsLegitimate = false },
                ["python-requests"] = new BotInfo { Name = "Python Requests", Type = BotType.Scraper, IsLegitimate = false },
                ["scrapy"] = new BotInfo { Name = "Scrapy", Type = BotType.Scraper, IsLegitimate = false }
            };
        }

        /// <summary>
        /// Initialize attack patterns
        /// </summary>
        private List<string> InitializeAttackPatterns()
        {
            return new List<string>
            {
                "union select", "drop table", "insert into", "delete from",
                "<script>", "javascript:", "onerror=", "onload=",
                "../", "..\\", "%2e%2e%2f", "%2e%2e%5c"
            };
        }

        /// <summary>
        /// Identify bot from User-Agent string
        /// </summary>
        private BotInfo? IdentifyBot(string userAgent)
        {
            if (string.IsNullOrEmpty(userAgent))
                return null;

            var lowerUserAgent = userAgent.ToLower();

            foreach (var bot in _knownBots)
            {
                if (lowerUserAgent.Contains(bot.Key.ToLower()))
                {
                    return bot.Value;
                }
            }

            // Check for generic bot patterns
            var botPatterns = new[] { "bot", "crawler", "spider", "scraper", "monitor" };
            if (botPatterns.Any(pattern => lowerUserAgent.Contains(pattern)))
            {
                return new BotInfo
                {
                    Name = "Unknown Bot",
                    UserAgent = userAgent,
                    Type = BotType.Unknown,
                    IsLegitimate = false
                };
            }

            return null;
        }

        /// <summary>
        /// Determine activity type for suspicious IP
        /// </summary>
        private SuspiciousActivityType DetermineActivityType(List<IISLogEntry> entries, double errorRate, int authFailures)
        {
            if (authFailures >= 10)
                return SuspiciousActivityType.AuthenticationBruteForce;
            if (errorRate >= 70)
                return SuspiciousActivityType.ScanningActivity;
            if (errorRate >= 30)
                return SuspiciousActivityType.HighErrorRate;

            var uniqueEndpoints = entries.Select(e => e.ApiEndpoint).Distinct().Count();
            if (uniqueEndpoints >= 20)
                return SuspiciousActivityType.ScanningActivity;

            return SuspiciousActivityType.AbnormalTraffic;
        }

        /// <summary>
        /// Calculate risk score for IP address
        /// </summary>
        private int CalculateRiskScore(List<IISLogEntry> entries, double errorRate, int authFailures)
        {
            int score = 0;

            // Error rate contribution
            score += (int)(errorRate / 2);

            // Authentication failures
            score += authFailures * 2;

            // Request volume
            if (entries.Count > 1000) score += 20;
            else if (entries.Count > 500) score += 10;

            // Unique endpoints accessed
            var uniqueEndpoints = entries.Select(e => e.ApiEndpoint).Distinct().Count();
            if (uniqueEndpoints > 50) score += 15;
            else if (uniqueEndpoints > 20) score += 10;

            // Time span (rapid requests)
            var timeSpan = entries.Max(e => e.DateTime) - entries.Min(e => e.DateTime);
            if (timeSpan.TotalHours < 1 && entries.Count > 100) score += 25;

            return Math.Min(100, score);
        }

        /// <summary>
        /// Calculate attack severity
        /// </summary>
        private int CalculateAttackSeverity(List<IISLogEntry> entries)
        {
            int severity = 1;

            // Volume
            if (entries.Count > 100) severity += 3;
            else if (entries.Count > 50) severity += 2;
            else if (entries.Count > 10) severity += 1;

            // Unique IPs
            var uniqueIPs = entries.Select(e => e.ClientIP).Distinct().Count();
            if (uniqueIPs > 10) severity += 2;
            else if (uniqueIPs > 5) severity += 1;

            // Time span
            var timeSpan = entries.Max(e => e.DateTime) - entries.Min(e => e.DateTime);
            if (timeSpan.TotalHours < 1) severity += 2;

            return Math.Min(10, severity);
        }

        /// <summary>
        /// Calculate overall security metrics
        /// </summary>
        private SecurityMetrics CalculateSecurityMetrics(SecurityAnalysis analysis, int totalRequests)
        {
            var metrics = new SecurityMetrics();

            // Count security events
            metrics.TotalSecurityEvents = analysis.AuthenticationAnalysis.TotalAuthFailures +
                                        analysis.PotentialAttacks.Sum(a => a.OccurrenceCount);

            // Categorize by severity
            metrics.HighRiskEvents = analysis.PotentialAttacks.Count(a => a.Severity >= 7) +
                                   analysis.SuspiciousIPs.Count(ip => ip.RiskScore >= 70);
            metrics.MediumRiskEvents = analysis.PotentialAttacks.Count(a => a.Severity >= 4 && a.Severity < 7) +
                                     analysis.SuspiciousIPs.Count(ip => ip.RiskScore >= 40 && ip.RiskScore < 70);
            metrics.LowRiskEvents = metrics.TotalSecurityEvents - metrics.HighRiskEvents - metrics.MediumRiskEvents;

            // Calculate overall security score
            double securityScore = 100;
            securityScore -= (double)analysis.AuthenticationAnalysis.TotalAuthFailures / totalRequests * 1000;
            securityScore -= analysis.SuspiciousIPs.Count * 5;
            securityScore -= analysis.PotentialAttacks.Sum(a => a.Severity);
            metrics.OverallSecurityScore = Math.Max(0, Math.Min(100, securityScore));

            // Threats by type
            metrics.ThreatsByType = analysis.PotentialAttacks
                .GroupBy(a => a.Type.ToString())
                .ToDictionary(g => g.Key, g => g.Sum(a => a.OccurrenceCount));

            // Generate recommendations
            metrics.SecurityRecommendations = GenerateSecurityRecommendations(analysis);

            return metrics;
        }

        /// <summary>
        /// Generate security recommendations
        /// </summary>
        private List<string> GenerateSecurityRecommendations(SecurityAnalysis analysis)
        {
            var recommendations = new List<string>();

            if (analysis.AuthenticationAnalysis.TotalAuthFailures > 100)
                recommendations.Add("Implement rate limiting for authentication endpoints");

            if (analysis.SuspiciousIPs.Any(ip => ip.RiskScore >= 80))
                recommendations.Add("Consider blocking high-risk IP addresses");

            if (analysis.PotentialAttacks.Any(a => a.Type == AttackType.SQLInjection))
                recommendations.Add("Review and strengthen SQL injection protection");

            if (analysis.PotentialAttacks.Any(a => a.Type == AttackType.XSSAttempt))
                recommendations.Add("Implement stronger XSS protection measures");

            if (analysis.BotAnalysis.BotTrafficPercentage > 50)
                recommendations.Add("Consider implementing bot detection and management");

            return recommendations;
        }
    }
}
