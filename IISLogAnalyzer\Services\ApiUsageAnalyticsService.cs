using System;
using System.Collections.Generic;
using System.Linq;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Services
{
    /// <summary>
    /// Service for analyzing API usage patterns and analytics
    /// </summary>
    public class ApiUsageAnalyticsService
    {
        /// <summary>
        /// Perform comprehensive API usage analytics
        /// </summary>
        public ApiUsageAnalytics AnalyzeApiUsage(List<IISLogEntry> logEntries)
        {
            if (!logEntries.Any())
                return new ApiUsageAnalytics();

            var analytics = new ApiUsageAnalytics
            {
                EndpointPopularity = AnalyzeEndpointPopularity(logEntries),
                RequestSizeAnalysis = AnalyzeRequestSizes(logEntries),
                GeographicAnalysis = AnalyzeGeographicDistribution(logEntries),
                RateLimitingAnalysis = AnalyzeRateLimiting(logEntries)
            };

            analytics.UsageMetrics = CalculateUsageMetrics(analytics, logEntries);

            return analytics;
        }

        /// <summary>
        /// Analyze endpoint popularity and ranking
        /// </summary>
        private List<EndpointPopularity> AnalyzeEndpointPopularity(List<IISLogEntry> logEntries)
        {
            var endpointGroups = logEntries.GroupBy(e => new { e.Method, e.ApiEndpoint });
            var popularityList = new List<EndpointPopularity>();

            foreach (var group in endpointGroups)
            {
                var entries = group.ToList();
                var uniqueUsers = entries.Select(e => e.ClientIP).Distinct().Count();
                var timeSpan = entries.Max(e => e.DateTime) - entries.Min(e => e.DateTime);
                var requestsPerHour = timeSpan.TotalHours > 0 ? entries.Count / timeSpan.TotalHours : entries.Count;

                var popularity = new EndpointPopularity
                {
                    Endpoint = group.Key.ApiEndpoint,
                    Method = group.Key.Method,
                    TotalRequests = entries.Count,
                    UniqueUsers = uniqueUsers,
                    RequestsPerHour = requestsPerHour,
                    AverageResponseTime = entries.Average(e => e.TimeTaken),
                    SuccessRate = (double)entries.Count(e => e.IsSuccessful) / entries.Count * 100,
                    Category = CategorizeEndpoint(group.Key.ApiEndpoint),
                    TopUserIPs = entries.GroupBy(e => e.ClientIP)
                        .OrderByDescending(g => g.Count())
                        .Take(5)
                        .Select(g => g.Key)
                        .ToList()
                };

                // Calculate popularity score
                popularity.PopularityScore = CalculatePopularityScore(popularity);

                // Usage over time
                popularity.UsageOverTime = entries
                    .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, 0, 0))
                    .ToDictionary(g => g.Key, g => g.Count());

                // Usage trend
                popularity.Trend = AnalyzeUsageTrend(popularity.UsageOverTime);

                popularityList.Add(popularity);
            }

            return popularityList.OrderByDescending(p => p.PopularityScore).ToList();
        }

        /// <summary>
        /// Analyze request size distribution
        /// </summary>
        private RequestSizeAnalysis AnalyzeRequestSizes(List<IISLogEntry> logEntries)
        {
            var analysis = new RequestSizeAnalysis();

            // Calculate overall statistics
            var requestSizes = logEntries.Select(e => e.BytesReceived).Where(size => size > 0).ToList();
            var responseSizes = logEntries.Select(e => e.BytesSent).Where(size => size > 0).ToList();

            if (requestSizes.Any())
            {
                analysis.AverageRequestSize = (long)requestSizes.Average();
                analysis.MedianRequestSize = CalculateMedian(requestSizes);
                analysis.MaxRequestSize = requestSizes.Max();
                analysis.MinRequestSize = requestSizes.Min();
            }

            // Size by endpoint
            var endpointGroups = logEntries.GroupBy(e => new { e.Method, e.ApiEndpoint });
            foreach (var group in endpointGroups.Where(g => g.Any(e => e.BytesReceived > 0)))
            {
                var entries = group.Where(e => e.BytesReceived > 0).ToList();
                if (!entries.Any()) continue;

                var sizes = entries.Select(e => e.BytesReceived).ToList();
                var key = $"{group.Key.Method} {group.Key.ApiEndpoint}";

                analysis.SizeByEndpoint[key] = new RequestSizeInfo
                {
                    Endpoint = group.Key.ApiEndpoint,
                    Method = group.Key.Method,
                    AverageSize = (long)sizes.Average(),
                    MedianSize = CalculateMedian(sizes),
                    MaxSize = sizes.Max(),
                    MinSize = sizes.Min(),
                    RequestCount = sizes.Count,
                    TotalSize = sizes.Sum(),
                    FormattedAverageSize = FormatBytes((long)sizes.Average()),
                    FormattedTotalSize = FormatBytes(sizes.Sum())
                };
            }

            // Size by method
            analysis.SizeByMethod = logEntries
                .Where(e => e.BytesReceived > 0)
                .GroupBy(e => e.Method)
                .ToDictionary(g => g.Key, g => (long)g.Average(e => e.BytesReceived));

            // Size distribution buckets
            analysis.SizeDistribution = CreateSizeDistribution(requestSizes);

            // Largest requests
            analysis.LargestRequests = logEntries
                .Where(e => e.BytesReceived > 0)
                .OrderByDescending(e => e.BytesReceived)
                .Take(20)
                .Select(e => new LargeRequest
                {
                    TimeStamp = e.DateTime,
                    ClientIP = e.ClientIP,
                    Method = e.Method,
                    Endpoint = e.ApiEndpoint,
                    RequestSize = e.BytesReceived,
                    ResponseSize = e.BytesSent,
                    ResponseTime = e.TimeTaken,
                    StatusCode = e.StatusCode,
                    UserAgent = e.UserAgent,
                    FormattedRequestSize = FormatBytes(e.BytesReceived),
                    FormattedResponseSize = FormatBytes(e.BytesSent)
                })
                .ToList();

            return analysis;
        }

        /// <summary>
        /// Analyze geographic distribution (simplified without actual geolocation)
        /// </summary>
        private GeographicAnalysis AnalyzeGeographicDistribution(List<IISLogEntry> logEntries)
        {
            var analysis = new GeographicAnalysis
            {
                GeolocationAvailable = false,
                DataSource = "IP Address Analysis (No Geolocation Service)"
            };

            // For now, we'll just analyze IP patterns without actual geolocation
            var ipGroups = logEntries.GroupBy(e => e.ClientIP);
            var ipAnalysis = new Dictionary<string, int>();

            foreach (var ipGroup in ipGroups)
            {
                var region = DetermineRegionFromIP(ipGroup.Key);
                ipAnalysis[region] = ipAnalysis.GetValueOrDefault(region, 0) + ipGroup.Count();
            }

            // Create mock country info
            foreach (var region in ipAnalysis)
            {
                analysis.CountryDistribution[region.Key] = new CountryInfo
                {
                    CountryCode = GetCountryCode(region.Key),
                    CountryName = region.Key,
                    RequestCount = region.Value,
                    UniqueIPs = logEntries.Where(e => DetermineRegionFromIP(e.ClientIP) == region.Key)
                        .Select(e => e.ClientIP).Distinct().Count(),
                    Percentage = (double)region.Value / logEntries.Count * 100,
                    PopularEndpoints = logEntries.Where(e => DetermineRegionFromIP(e.ClientIP) == region.Key)
                        .GroupBy(e => e.ApiEndpoint)
                        .OrderByDescending(g => g.Count())
                        .Take(5)
                        .Select(g => g.Key)
                        .ToList(),
                    AverageResponseTime = logEntries.Where(e => DetermineRegionFromIP(e.ClientIP) == region.Key)
                        .Average(e => e.TimeTaken)
                };
            }

            analysis.TopCountries = analysis.CountryDistribution
                .OrderByDescending(kvp => kvp.Value.RequestCount)
                .Take(10)
                .Select(kvp => kvp.Key)
                .ToList();

            return analysis;
        }

        /// <summary>
        /// Analyze rate limiting violations and patterns
        /// </summary>
        private RateLimitingAnalysis AnalyzeRateLimiting(List<IISLogEntry> logEntries)
        {
            var analysis = new RateLimitingAnalysis();

            // Analyze request patterns by IP
            var ipGroups = logEntries.GroupBy(e => e.ClientIP);

            foreach (var ipGroup in ipGroups)
            {
                var entries = ipGroup.OrderBy(e => e.DateTime).ToList();

                // Check for burst traffic (many requests in short time)
                var ipViolations = DetectRateLimitViolations(entries, ipGroup.Key);
                analysis.Violations.AddRange(ipViolations);
            }

            // Violations by IP
            analysis.ViolationsByIP = analysis.Violations
                .GroupBy(v => v.ClientIP)
                .ToDictionary(g => g.Key, g => g.Count());

            // Violations by endpoint
            analysis.ViolationsByEndpoint = analysis.Violations
                .GroupBy(v => v.Endpoint)
                .ToDictionary(g => g.Key, g => g.Count());

            analysis.TotalViolations = analysis.Violations.Count;

            // Frequent violators
            analysis.FrequentViolators = analysis.ViolationsByIP
                .Where(kvp => kvp.Value >= 5)
                .OrderByDescending(kvp => kvp.Value)
                .Take(10)
                .Select(kvp => kvp.Key)
                .ToList();

            // Violations over time
            analysis.ViolationsOverTime = analysis.Violations
                .GroupBy(v => new DateTime(v.TimeStamp.Year, v.TimeStamp.Month, v.TimeStamp.Day, v.TimeStamp.Hour, 0, 0))
                .ToDictionary(g => g.Key, g => g.Count());

            // Calculate metrics
            analysis.Metrics = CalculateRateLimitingMetrics(logEntries);

            return analysis;
        }

        /// <summary>
        /// Calculate overall usage metrics
        /// </summary>
        private ApiUsageMetrics CalculateUsageMetrics(ApiUsageAnalytics analytics, List<IISLogEntry> logEntries)
        {
            var metrics = new ApiUsageMetrics
            {
                TotalApiCalls = logEntries.Count,
                UniqueEndpoints = analytics.EndpointPopularity.Count,
                UniqueUsers = logEntries.Select(e => e.ClientIP).Distinct().Count()
            };

            metrics.AverageCallsPerUser = metrics.UniqueUsers > 0 ? (double)metrics.TotalApiCalls / metrics.UniqueUsers : 0;
            metrics.AverageCallsPerEndpoint = metrics.UniqueEndpoints > 0 ? (double)metrics.TotalApiCalls / metrics.UniqueEndpoints : 0;

            // Method distribution
            metrics.MethodDistribution = logEntries
                .GroupBy(e => e.Method)
                .ToDictionary(g => g.Key, g => g.Count());

            // Endpoint utilization
            metrics.EndpointUtilization = analytics.EndpointPopularity
                .ToDictionary(ep => ep.Endpoint, ep => ep.RequestsPerHour);

            // Under/over utilized endpoints
            var avgUtilization = metrics.EndpointUtilization.Values.Average();
            metrics.UnderutilizedEndpoints = metrics.EndpointUtilization
                .Where(kvp => kvp.Value < avgUtilization * 0.1)
                .Select(kvp => kvp.Key)
                .ToList();

            metrics.OverutilizedEndpoints = metrics.EndpointUtilization
                .Where(kvp => kvp.Value > avgUtilization * 5)
                .Select(kvp => kvp.Key)
                .ToList();

            // Health score
            metrics.HealthScore = CalculateApiHealthScore(analytics, logEntries);

            return metrics;
        }

        /// <summary>
        /// Categorize endpoint based on URL pattern
        /// </summary>
        private EndpointCategory CategorizeEndpoint(string endpoint)
        {
            var lowerEndpoint = endpoint.ToLower();

            if (lowerEndpoint.Contains("/api/"))
                return EndpointCategory.API;
            if (lowerEndpoint.Contains("/auth") || lowerEndpoint.Contains("/login") || lowerEndpoint.Contains("/oauth"))
                return EndpointCategory.Authentication;
            if (lowerEndpoint.Contains("/admin"))
                return EndpointCategory.Admin;
            if (lowerEndpoint.Contains("/internal"))
                return EndpointCategory.Internal;
            if (lowerEndpoint.Contains(".css") || lowerEndpoint.Contains(".js") || lowerEndpoint.Contains(".png") ||
                lowerEndpoint.Contains(".jpg") || lowerEndpoint.Contains(".gif") || lowerEndpoint.Contains(".ico"))
                return EndpointCategory.StaticContent;

            return EndpointCategory.Public;
        }

        /// <summary>
        /// Calculate popularity score for endpoint
        /// </summary>
        private double CalculatePopularityScore(EndpointPopularity popularity)
        {
            double score = 0;

            // Request volume (40% weight)
            score += Math.Log10(Math.Max(1, popularity.TotalRequests)) * 40;

            // Unique users (30% weight)
            score += Math.Log10(Math.Max(1, popularity.UniqueUsers)) * 30;

            // Success rate (20% weight)
            score += popularity.SuccessRate * 0.2;

            // Request frequency (10% weight)
            score += Math.Log10(Math.Max(1, popularity.RequestsPerHour)) * 10;

            return score;
        }

        /// <summary>
        /// Analyze usage trend for endpoint
        /// </summary>
        private UsageTrend AnalyzeUsageTrend(Dictionary<DateTime, int> usageOverTime)
        {
            var trend = new UsageTrend();

            if (usageOverTime.Count < 2)
            {
                trend.Direction = TrendDirection.Stable;
                return trend;
            }

            var sortedUsage = usageOverTime.OrderBy(kvp => kvp.Key).ToList();
            var firstHalf = sortedUsage.Take(sortedUsage.Count / 2).Sum(kvp => kvp.Value);
            var secondHalf = sortedUsage.Skip(sortedUsage.Count / 2).Sum(kvp => kvp.Value);

            var changePercentage = firstHalf > 0 ? (double)(secondHalf - firstHalf) / firstHalf * 100 : 0;
            trend.ChangePercentage = changePercentage;

            if (changePercentage > 20)
            {
                trend.Direction = TrendDirection.Improving; // More usage is "improving" for popularity
                trend.IsGrowing = true;
            }
            else if (changePercentage < -20)
            {
                trend.Direction = TrendDirection.Worsening;
                trend.IsDeclining = true;
            }
            else
            {
                trend.Direction = TrendDirection.Stable;
                trend.IsStable = true;
            }

            trend.RequestsOverTime = usageOverTime;
            return trend;
        }

        /// <summary>
        /// Calculate median value from list
        /// </summary>
        private long CalculateMedian(List<long> values)
        {
            if (!values.Any()) return 0;

            var sorted = values.OrderBy(v => v).ToList();
            var mid = sorted.Count / 2;

            if (sorted.Count % 2 == 0)
                return (sorted[mid - 1] + sorted[mid]) / 2;
            else
                return sorted[mid];
        }

        /// <summary>
        /// Create size distribution buckets
        /// </summary>
        private Dictionary<string, long> CreateSizeDistribution(List<long> sizes)
        {
            var distribution = new Dictionary<string, long>();

            if (!sizes.Any()) return distribution;

            var buckets = new[]
            {
                ("0-1KB", 0L, 1024L),
                ("1-10KB", 1024L, 10240L),
                ("10-100KB", 10240L, 102400L),
                ("100KB-1MB", 102400L, 1048576L),
                ("1-10MB", 1048576L, 10485760L),
                ("10MB+", 10485760L, long.MaxValue)
            };

            foreach (var bucket in buckets)
            {
                var count = sizes.Count(s => s >= bucket.Item2 && s < bucket.Item3);
                distribution[bucket.Item1] = count;
            }

            return distribution;
        }

        /// <summary>
        /// Determine region from IP address (simplified)
        /// </summary>
        private string DetermineRegionFromIP(string ipAddress)
        {
            // This is a simplified implementation
            // In a real scenario, you would use a geolocation service

            if (ipAddress.StartsWith("192.168.") || ipAddress.StartsWith("10.") || ipAddress.StartsWith("172."))
                return "Local Network";
            if (ipAddress.StartsWith("127."))
                return "Localhost";

            // Mock geographic distribution based on IP patterns
            var hash = ipAddress.GetHashCode();
            var regions = new[] { "North America", "Europe", "Asia", "South America", "Africa", "Oceania", "Unknown" };
            return regions[Math.Abs(hash) % regions.Length];
        }

        /// <summary>
        /// Get country code for region
        /// </summary>
        private string GetCountryCode(string region)
        {
            return region switch
            {
                "North America" => "US",
                "Europe" => "EU",
                "Asia" => "AS",
                "South America" => "SA",
                "Africa" => "AF",
                "Oceania" => "OC",
                "Local Network" => "LAN",
                "Localhost" => "LOC",
                _ => "XX"
            };
        }

        /// <summary>
        /// Detect rate limiting violations
        /// </summary>
        private List<RateLimitViolation> DetectRateLimitViolations(List<IISLogEntry> entries, string clientIP)
        {
            var violations = new List<RateLimitViolation>();
            var timeWindows = new[] { TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(15) };
            var limits = new[] { 100, 300, 600 }; // Requests per window

            for (int i = 0; i < timeWindows.Length; i++)
            {
                var window = timeWindows[i];
                var limit = limits[i];

                for (int j = 0; j < entries.Count; j++)
                {
                    var windowStart = entries[j].DateTime;
                    var windowEnd = windowStart.Add(window);

                    var requestsInWindow = entries.Skip(j).TakeWhile(e => e.DateTime <= windowEnd).Count();

                    if (requestsInWindow > limit)
                    {
                        violations.Add(new RateLimitViolation
                        {
                            TimeStamp = windowStart,
                            ClientIP = clientIP,
                            Endpoint = entries[j].ApiEndpoint,
                            Method = entries[j].Method,
                            RequestsInWindow = requestsInWindow,
                            TimeWindow = window,
                            AllowedLimit = limit,
                            Type = DetermineViolationType(requestsInWindow, limit),
                            Severity = CalculateViolationSeverity(requestsInWindow, limit)
                        });

                        // Skip ahead to avoid duplicate violations
                        j += Math.Min(requestsInWindow / 2, 50);
                    }
                }
            }

            return violations;
        }

        /// <summary>
        /// Determine violation type
        /// </summary>
        private ViolationType DetermineViolationType(int requests, int limit)
        {
            var ratio = (double)requests / limit;

            if (ratio > 5) return ViolationType.Suspicious;
            if (ratio > 3) return ViolationType.Automated;
            if (ratio > 2) return ViolationType.SustainedHigh;
            return ViolationType.BurstTraffic;
        }

        /// <summary>
        /// Calculate violation severity
        /// </summary>
        private int CalculateViolationSeverity(int requests, int limit)
        {
            var ratio = (double)requests / limit;
            return Math.Min(10, (int)(ratio * 2));
        }

        /// <summary>
        /// Calculate rate limiting metrics
        /// </summary>
        private RateLimitingMetrics CalculateRateLimitingMetrics(List<IISLogEntry> logEntries)
        {
            var metrics = new RateLimitingMetrics();

            var timeSpan = logEntries.Max(e => e.DateTime) - logEntries.Min(e => e.DateTime);
            metrics.AverageRequestsPerMinute = timeSpan.TotalMinutes > 0 ? logEntries.Count / timeSpan.TotalMinutes : 0;

            // Peak requests per minute
            var minutelyRequests = logEntries
                .GroupBy(e => new DateTime(e.DateTime.Year, e.DateTime.Month, e.DateTime.Day, e.DateTime.Hour, e.DateTime.Minute, 0))
                .Select(g => g.Count());

            metrics.PeakRequestsPerMinute = minutelyRequests.Any() ? minutelyRequests.Max() : 0;

            // Request rate by IP
            var ipGroups = logEntries.GroupBy(e => e.ClientIP);
            metrics.RequestRateByIP = ipGroups.ToDictionary(
                g => g.Key,
                g => timeSpan.TotalMinutes > 0 ? g.Count() / timeSpan.TotalMinutes : 0
            );

            // Request rate by endpoint
            var endpointGroups = logEntries.GroupBy(e => e.ApiEndpoint);
            metrics.RequestRateByEndpoint = endpointGroups.ToDictionary(
                g => g.Key,
                g => timeSpan.TotalMinutes > 0 ? g.Count() / timeSpan.TotalMinutes : 0
            );

            // Generate recommendations
            metrics.RecommendedRateLimits = GenerateRateLimitRecommendations(metrics);

            return metrics;
        }

        /// <summary>
        /// Generate rate limit recommendations
        /// </summary>
        private List<string> GenerateRateLimitRecommendations(RateLimitingMetrics metrics)
        {
            var recommendations = new List<string>();

            if (metrics.PeakRequestsPerMinute > 1000)
                recommendations.Add("Consider implementing aggressive rate limiting (100 requests/minute)");
            else if (metrics.PeakRequestsPerMinute > 500)
                recommendations.Add("Implement moderate rate limiting (200 requests/minute)");
            else if (metrics.PeakRequestsPerMinute > 100)
                recommendations.Add("Implement basic rate limiting (300 requests/minute)");

            var topIPs = metrics.RequestRateByIP.OrderByDescending(kvp => kvp.Value).Take(5);
            foreach (var ip in topIPs.Where(kvp => kvp.Value > 50))
            {
                recommendations.Add($"Monitor IP {ip.Key} - {ip.Value:F1} requests/minute");
            }

            return recommendations;
        }

        /// <summary>
        /// Calculate API health score
        /// </summary>
        private ApiHealthScore CalculateApiHealthScore(ApiUsageAnalytics analytics, List<IISLogEntry> logEntries)
        {
            var healthScore = new ApiHealthScore();

            // Performance score (based on response times)
            var avgResponseTime = logEntries.Average(e => e.TimeTaken);
            healthScore.PerformanceScore = Math.Max(0, 100 - (avgResponseTime / 50)); // 50ms = 1 point deduction

            // Reliability score (based on success rate)
            var successRate = (double)logEntries.Count(e => e.IsSuccessful) / logEntries.Count * 100;
            healthScore.ReliabilityScore = successRate;

            // Security score (based on violations and suspicious activity)
            var violationRate = (double)analytics.RateLimitingAnalysis.TotalViolations / logEntries.Count * 100;
            healthScore.SecurityScore = Math.Max(0, 100 - (violationRate * 10));

            // Usability score (based on error rates and documentation)
            var errorRate = (double)logEntries.Count(e => !e.IsSuccessful) / logEntries.Count * 100;
            healthScore.UsabilityScore = Math.Max(0, 100 - (errorRate * 2));

            // Overall score
            healthScore.OverallScore = (healthScore.PerformanceScore + healthScore.ReliabilityScore +
                                     healthScore.SecurityScore + healthScore.UsabilityScore) / 4;

            // Generate recommendations and warnings
            healthScore.Recommendations = GenerateHealthRecommendations(healthScore);
            healthScore.Warnings = GenerateHealthWarnings(healthScore, analytics);

            return healthScore;
        }

        /// <summary>
        /// Generate health recommendations
        /// </summary>
        private List<string> GenerateHealthRecommendations(ApiHealthScore healthScore)
        {
            var recommendations = new List<string>();

            if (healthScore.PerformanceScore < 70)
                recommendations.Add("Optimize response times - consider caching and database optimization");
            if (healthScore.ReliabilityScore < 95)
                recommendations.Add("Improve error handling and system reliability");
            if (healthScore.SecurityScore < 80)
                recommendations.Add("Strengthen security measures and rate limiting");
            if (healthScore.UsabilityScore < 85)
                recommendations.Add("Improve API documentation and error messages");

            return recommendations;
        }

        /// <summary>
        /// Generate health warnings
        /// </summary>
        private List<string> GenerateHealthWarnings(ApiHealthScore healthScore, ApiUsageAnalytics analytics)
        {
            var warnings = new List<string>();

            if (healthScore.OverallScore < 50)
                warnings.Add("Critical: API health is poor - immediate attention required");
            if (analytics.RateLimitingAnalysis.TotalViolations > 100)
                warnings.Add("High number of rate limit violations detected");
            if (analytics.EndpointPopularity.Any(ep => ep.SuccessRate < 90))
                warnings.Add("Some endpoints have low success rates");

            return warnings;
        }

        /// <summary>
        /// Format bytes to human readable format
        /// </summary>
        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;

            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }

            return $"{number:n1} {suffixes[counter]}";
        }
    }
}
