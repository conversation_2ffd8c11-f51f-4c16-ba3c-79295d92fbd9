IISLogger Project Documentation

1. Project Overview
-------------------
IISLogger is a Windows desktop application designed for analyzing IIS log files and providing comprehensive API-wise results and insights. It features advanced log parsing, detailed API analysis, data visualization, and multiple export formats, all within a modern Windows Forms interface.

2. Key Features
---------------
- Advanced log parsing for W3C Extended Log Format
- Batch processing and real-time progress tracking
- Comprehensive API analysis (metrics, performance, traffic, status codes)
- Advanced filtering and search (text, date, status code, response time, volume)
- Enhanced user interface with tabbed data visualization and progress tracking
- Data visualization (charts, summaries, breakdowns)
- Multiple export formats: CSV, JSON, HTML, Text
- Configurable settings for analysis, performance, and UI
- Robust error handling and logging

3. Architecture
---------------
Core Components:
- IISLogParser: Parses IIS log files with progress tracking
- ApiAnalysisService: Analysis engine for metrics and filtering
- ExportService: Handles export to CSV, JSON, HTML, and Text
- LoggingService: Centralized logging and error tracking
- ErrorHandler: Global error handling
- MainForm: Main Windows Forms UI
- AdvancedFilterForm: Advanced filtering interface
- ChartsForm: Data visualization interface
- SettingsForm: Application configuration interface

Data Models:
- IISLogEntry: Represents a single log entry
- ApiAnalysisResult: Analysis results for API endpoints
- OverallAnalysisSummary: Aggregated analysis
- FilterCriteria: Filtering parameters
- ApplicationSettings: Configuration management

Services & Utilities:
- LoggingService: Singleton logger
- ErrorHandler: Static error handler
- Settings Management: JSON-based configuration

4. Usage Instructions
---------------------
- Load log files via file or folder selection
- Configure analysis and UI settings as needed
- View results in sortable, color-coded grids
- Apply basic or advanced filters
- Visualize data with charts and summaries
- Export results in CSV, JSON, HTML, or Text formats
- Access help and logs via the Help menu

5. Directory Structure
----------------------
- IISLogger.sln: Solution file
- README.md: Project documentation
- build_release.bat, run_analyzer.bat: Build and run scripts
- IISLogAnalyzer/: Main application source code
  - Forms/: UI forms (AdvancedFilterForm, ChartsForm, SettingsForm)
  - Models/: Data models (IISLogEntry, ApiAnalysisResult, etc.)
  - Services/: Core services (parsing, analysis, export, logging)
  - Utilities/: Utility classes (ErrorHandler)
  - SampleData/: Sample IIS log file
  - Program.cs: Application entry point

6. Sample Data
--------------
Sample IIS log data is available in SampleData/sample_iis.log for testing and demonstration purposes.

7. Requirements
---------------
- Windows 10/11
- .NET 9.0 Runtime or later

8. License
----------
This project is licensed under the MIT License.

For more details, refer to the README.md in the project root.