using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Windows.Forms;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Forms
{
    /// <summary>
    /// Advanced filtering options for IIS log analysis results
    /// </summary>
    public partial class AdvancedFilterForm : Form
    {
        private FilterCriteria _filterCriteria = new();

        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public FilterCriteria FilterCriteria
        {
            get => _filterCriteria;
            set => _filterCriteria = value;
        }

        public AdvancedFilterForm()
        {
            InitializeComponent();
            InitializeFilters();
        }

        public AdvancedFilterForm(FilterCriteria existingCriteria) : this()
        {
            LoadExistingCriteria(existingCriteria);
        }

        private void InitializeFilters()
        {
            // Initialize HTTP method checkboxes
            var methods = new[] { "GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS" };
            foreach (var method in methods)
            {
                var checkbox = new CheckBox
                {
                    Text = method,
                    Checked = true,
                    AutoSize = true
                };
                flowLayoutPanelMethods.Controls.Add(checkbox);
            }

            // Initialize status code ranges
            comboBoxStatusCode.Items.AddRange(new[]
            {
                "All Status Codes",
                "2xx - Success (200-299)",
                "3xx - Redirection (300-399)",
                "4xx - Client Error (400-499)",
                "5xx - Server Error (500-599)",
                "Custom Range"
            });
            comboBoxStatusCode.SelectedIndex = 0;

            // Set default date range to last 7 days
            dateTimePickerFrom.Value = DateTime.Now.AddDays(-7);
            dateTimePickerTo.Value = DateTime.Now;

            // Set default response time range
            numericUpDownMinResponseTime.Value = 0;
            numericUpDownMaxResponseTime.Value = 10000; // 10 seconds
        }

        private void LoadExistingCriteria(FilterCriteria criteria)
        {
            // Load API endpoint filter
            textBoxApiEndpoint.Text = criteria.ApiEndpointFilter;

            // Load HTTP methods
            foreach (CheckBox checkbox in flowLayoutPanelMethods.Controls.OfType<CheckBox>())
            {
                checkbox.Checked = criteria.HttpMethods.Contains(checkbox.Text);
            }

            // Load date range
            if (criteria.DateFrom.HasValue)
            {
                checkBoxDateRange.Checked = true;
                dateTimePickerFrom.Value = criteria.DateFrom.Value;
            }
            if (criteria.DateTo.HasValue)
            {
                checkBoxDateRange.Checked = true;
                dateTimePickerTo.Value = criteria.DateTo.Value;
            }

            // Load status code filter
            if (criteria.StatusCodeFrom.HasValue && criteria.StatusCodeTo.HasValue)
            {
                var from = criteria.StatusCodeFrom.Value;
                var to = criteria.StatusCodeTo.Value;
                
                if (from >= 200 && to <= 299)
                    comboBoxStatusCode.SelectedIndex = 1;
                else if (from >= 300 && to <= 399)
                    comboBoxStatusCode.SelectedIndex = 2;
                else if (from >= 400 && to <= 499)
                    comboBoxStatusCode.SelectedIndex = 3;
                else if (from >= 500 && to <= 599)
                    comboBoxStatusCode.SelectedIndex = 4;
                else
                {
                    comboBoxStatusCode.SelectedIndex = 5;
                    numericUpDownStatusFrom.Value = from;
                    numericUpDownStatusTo.Value = to;
                }
            }

            // Load response time filter
            if (criteria.MinResponseTime.HasValue)
            {
                checkBoxResponseTime.Checked = true;
                numericUpDownMinResponseTime.Value = criteria.MinResponseTime.Value;
            }
            if (criteria.MaxResponseTime.HasValue)
            {
                checkBoxResponseTime.Checked = true;
                numericUpDownMaxResponseTime.Value = criteria.MaxResponseTime.Value;
            }

            // Load minimum requests filter
            if (criteria.MinimumRequests.HasValue)
            {
                checkBoxMinRequests.Checked = true;
                numericUpDownMinRequests.Value = criteria.MinimumRequests.Value;
            }

            // Load unique clients filter
            if (criteria.MinimumUniqueClients.HasValue)
            {
                checkBoxMinClients.Checked = true;
                numericUpDownMinClients.Value = criteria.MinimumUniqueClients.Value;
            }
        }

        private void btnApply_Click(object sender, EventArgs e)
        {
            FilterCriteria = BuildFilterCriteria();
            DialogResult = DialogResult.OK;
            Close();
        }

        private void btnReset_Click(object sender, EventArgs e)
        {
            // Reset all controls to default values
            textBoxApiEndpoint.Clear();
            
            foreach (CheckBox checkbox in flowLayoutPanelMethods.Controls.OfType<CheckBox>())
            {
                checkbox.Checked = true;
            }
            
            checkBoxDateRange.Checked = false;
            dateTimePickerFrom.Value = DateTime.Now.AddDays(-7);
            dateTimePickerTo.Value = DateTime.Now;
            
            comboBoxStatusCode.SelectedIndex = 0;
            
            checkBoxResponseTime.Checked = false;
            numericUpDownMinResponseTime.Value = 0;
            numericUpDownMaxResponseTime.Value = 10000;
            
            checkBoxMinRequests.Checked = false;
            numericUpDownMinRequests.Value = 1;
            
            checkBoxMinClients.Checked = false;
            numericUpDownMinClients.Value = 1;
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
            Close();
        }

        private FilterCriteria BuildFilterCriteria()
        {
            var criteria = new FilterCriteria
            {
                ApiEndpointFilter = textBoxApiEndpoint.Text.Trim()
            };

            // Get selected HTTP methods
            criteria.HttpMethods = flowLayoutPanelMethods.Controls
                .OfType<CheckBox>()
                .Where(cb => cb.Checked)
                .Select(cb => cb.Text)
                .ToList();

            // Get date range
            if (checkBoxDateRange.Checked)
            {
                criteria.DateFrom = dateTimePickerFrom.Value.Date;
                criteria.DateTo = dateTimePickerTo.Value.Date.AddDays(1).AddTicks(-1);
            }

            // Get status code range
            switch (comboBoxStatusCode.SelectedIndex)
            {
                case 1: // 2xx
                    criteria.StatusCodeFrom = 200;
                    criteria.StatusCodeTo = 299;
                    break;
                case 2: // 3xx
                    criteria.StatusCodeFrom = 300;
                    criteria.StatusCodeTo = 399;
                    break;
                case 3: // 4xx
                    criteria.StatusCodeFrom = 400;
                    criteria.StatusCodeTo = 499;
                    break;
                case 4: // 5xx
                    criteria.StatusCodeFrom = 500;
                    criteria.StatusCodeTo = 599;
                    break;
                case 5: // Custom
                    criteria.StatusCodeFrom = (int)numericUpDownStatusFrom.Value;
                    criteria.StatusCodeTo = (int)numericUpDownStatusTo.Value;
                    break;
            }

            // Get response time range
            if (checkBoxResponseTime.Checked)
            {
                criteria.MinResponseTime = (int)numericUpDownMinResponseTime.Value;
                criteria.MaxResponseTime = (int)numericUpDownMaxResponseTime.Value;
            }

            // Get minimum requests
            if (checkBoxMinRequests.Checked)
            {
                criteria.MinimumRequests = (int)numericUpDownMinRequests.Value;
            }

            // Get minimum unique clients
            if (checkBoxMinClients.Checked)
            {
                criteria.MinimumUniqueClients = (int)numericUpDownMinClients.Value;
            }

            return criteria;
        }

        private void comboBoxStatusCode_SelectedIndexChanged(object sender, EventArgs e)
        {
            // Show/hide custom status code range controls
            bool showCustom = comboBoxStatusCode.SelectedIndex == 5;
            labelStatusFrom.Visible = showCustom;
            labelStatusTo.Visible = showCustom;
            numericUpDownStatusFrom.Visible = showCustom;
            numericUpDownStatusTo.Visible = showCustom;
        }

        private void checkBoxDateRange_CheckedChanged(object sender, EventArgs e)
        {
            dateTimePickerFrom.Enabled = checkBoxDateRange.Checked;
            dateTimePickerTo.Enabled = checkBoxDateRange.Checked;
        }

        private void checkBoxResponseTime_CheckedChanged(object sender, EventArgs e)
        {
            numericUpDownMinResponseTime.Enabled = checkBoxResponseTime.Checked;
            numericUpDownMaxResponseTime.Enabled = checkBoxResponseTime.Checked;
        }

        private void checkBoxMinRequests_CheckedChanged(object sender, EventArgs e)
        {
            numericUpDownMinRequests.Enabled = checkBoxMinRequests.Checked;
        }

        private void checkBoxMinClients_CheckedChanged(object sender, EventArgs e)
        {
            numericUpDownMinClients.Enabled = checkBoxMinClients.Checked;
        }
    }
}
