using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Services
{
    /// <summary>
    /// Service for analyzing user behavior patterns from IIS log data
    /// </summary>
    public class UserBehaviorAnalysisService
    {
        private readonly Dictionary<string, string> _browserPatterns;
        private readonly Dictionary<string, string> _osPatterns;
        private readonly Dictionary<string, string> _devicePatterns;

        public UserBehaviorAnalysisService()
        {
            _browserPatterns = InitializeBrowserPatterns();
            _osPatterns = InitializeOSPatterns();
            _devicePatterns = InitializeDevicePatterns();
        }

        /// <summary>
        /// Perform comprehensive user behavior analysis
        /// </summary>
        public UserBehaviorAnalysis AnalyzeUserBehavior(List<IISLogEntry> logEntries)
        {
            if (!logEntries.Any())
                return new UserBehaviorAnalysis();

            var analysis = new UserBehaviorAnalysis
            {
                MostActiveIPs = AnalyzeIPActivity(logEntries),
                UserAgentAnalysis = AnalyzeUserAgents(logEntries),
                ReferrerAnalysis = AnalyzeReferrers(logEntries),
                EstimatedSessions = EstimateSessions(logEntries)
            };

            analysis.BehaviorMetrics = CalculateBehaviorMetrics(analysis, logEntries);

            return analysis;
        }

        /// <summary>
        /// Analyze IP address activity patterns
        /// </summary>
        private List<IPActivityAnalysis> AnalyzeIPActivity(List<IISLogEntry> logEntries)
        {
            var ipGroups = logEntries.GroupBy(e => e.ClientIP);
            var ipAnalyses = new List<IPActivityAnalysis>();

            foreach (var ipGroup in ipGroups.Where(g => g.Count() >= 5)) // Filter out single-request IPs
            {
                var entries = ipGroup.OrderBy(e => e.DateTime).ToList();
                var uniqueEndpoints = entries.Select(e => e.ApiEndpoint).Distinct().ToList();
                var activityDuration = entries.Last().DateTime - entries.First().DateTime;

                var analysis = new IPActivityAnalysis
                {
                    IPAddress = ipGroup.Key,
                    TotalRequests = entries.Count,
                    UniqueEndpoints = uniqueEndpoints.Count,
                    FirstRequest = entries.First().DateTime,
                    LastRequest = entries.Last().DateTime,
                    ActivityDuration = activityDuration,
                    RequestsPerHour = activityDuration.TotalHours > 0 ? entries.Count / activityDuration.TotalHours : entries.Count,
                    RequestedEndpoints = uniqueEndpoints,
                    EndpointFrequency = entries.GroupBy(e => e.ApiEndpoint).ToDictionary(g => g.Key, g => g.Count()),
                    UserAgents = entries.Select(e => e.UserAgent).Distinct().ToList(),
                    StatusCodeDistribution = entries.GroupBy(e => e.StatusCode).ToDictionary(g => g.Key, g => g.Count()),
                    UserType = DetermineUserType(entries),
                    RequestPatterns = AnalyzeRequestPatterns(entries)
                };

                ipAnalyses.Add(analysis);
            }

            return ipAnalyses.OrderByDescending(ip => ip.TotalRequests).Take(50).ToList();
        }

        /// <summary>
        /// Analyze User-Agent strings for browser, device, and OS information
        /// </summary>
        private UserAgentAnalysis AnalyzeUserAgents(List<IISLogEntry> logEntries)
        {
            var analysis = new UserAgentAnalysis();
            var userAgents = logEntries.Select(e => e.UserAgent).Where(ua => !string.IsNullOrEmpty(ua)).ToList();

            // User-Agent distribution
            analysis.UserAgentDistribution = userAgents
                .GroupBy(ua => ua)
                .ToDictionary(g => g.Key, g => g.Count());

            analysis.TotalUniqueUserAgents = analysis.UserAgentDistribution.Count;

            // Browser analysis
            analysis.BrowserAnalysis = AnalyzeBrowsers(userAgents);

            // Device analysis
            analysis.DeviceAnalysis = AnalyzeDevices(userAgents);

            // OS analysis
            analysis.OSAnalysis = AnalyzeOperatingSystems(userAgents);

            // Unknown user agents
            analysis.UnknownUserAgents = userAgents
                .Where(ua => !IsKnownUserAgent(ua))
                .Distinct()
                .Take(20)
                .ToList();

            return analysis;
        }

        /// <summary>
        /// Analyze referrer patterns to understand traffic sources
        /// </summary>
        private ReferrerAnalysis AnalyzeReferrers(List<IISLogEntry> logEntries)
        {
            var analysis = new ReferrerAnalysis();
            var referrers = logEntries.Where(e => !string.IsNullOrEmpty(e.Referer) && e.Referer != "-").ToList();

            // Referrer distribution
            analysis.ReferrerDistribution = referrers
                .GroupBy(e => e.Referer)
                .ToDictionary(g => g.Key, g => g.Count());

            // Categorize referrers
            var searchEngines = new Dictionary<string, int>();
            var socialMedia = new Dictionary<string, int>();
            var directTraffic = new Dictionary<string, int>();

            foreach (var referrer in analysis.ReferrerDistribution)
            {
                var category = CategorizeReferrer(referrer.Key);
                switch (category)
                {
                    case "Search":
                        searchEngines[referrer.Key] = referrer.Value;
                        break;
                    case "Social":
                        socialMedia[referrer.Key] = referrer.Value;
                        break;
                    case "Direct":
                        directTraffic[referrer.Key] = referrer.Value;
                        break;
                }
            }

            analysis.SearchEngines = searchEngines;
            analysis.SocialMedia = socialMedia;
            analysis.DirectTraffic = directTraffic;

            // Calculate percentages
            var totalReferrers = analysis.ReferrerDistribution.Values.Sum();
            var directCount = logEntries.Count(e => string.IsNullOrEmpty(e.Referer) || e.Referer == "-");
            var totalRequests = logEntries.Count;

            analysis.DirectTrafficPercentage = (double)directCount / totalRequests * 100;
            analysis.SearchEnginePercentage = (double)searchEngines.Values.Sum() / totalRequests * 100;
            analysis.SocialMediaPercentage = (double)socialMedia.Values.Sum() / totalRequests * 100;
            analysis.OtherReferrerPercentage = 100 - analysis.DirectTrafficPercentage - 
                                             analysis.SearchEnginePercentage - analysis.SocialMediaPercentage;

            // Top traffic sources
            analysis.TopTrafficSources = CreateTrafficSources(analysis, logEntries);

            return analysis;
        }

        /// <summary>
        /// Estimate user sessions based on IP and time patterns
        /// </summary>
        private List<UserSession> EstimateSessions(List<IISLogEntry> logEntries)
        {
            var sessions = new List<UserSession>();
            var sessionTimeout = TimeSpan.FromMinutes(30); // Standard session timeout

            var ipGroups = logEntries.GroupBy(e => new { e.ClientIP, e.UserAgent });

            foreach (var ipGroup in ipGroups)
            {
                var entries = ipGroup.OrderBy(e => e.DateTime).ToList();
                var currentSession = new List<IISLogEntry>();

                foreach (var entry in entries)
                {
                    if (!currentSession.Any() || 
                        entry.DateTime - currentSession.Last().DateTime <= sessionTimeout)
                    {
                        currentSession.Add(entry);
                    }
                    else
                    {
                        // End current session and start new one
                        if (currentSession.Any())
                        {
                            sessions.Add(CreateUserSession(currentSession, ipGroup.Key.ClientIP, ipGroup.Key.UserAgent));
                        }
                        currentSession = new List<IISLogEntry> { entry };
                    }
                }

                // Add the last session
                if (currentSession.Any())
                {
                    sessions.Add(CreateUserSession(currentSession, ipGroup.Key.ClientIP, ipGroup.Key.UserAgent));
                }
            }

            return sessions.OrderByDescending(s => s.Duration).ToList();
        }

        /// <summary>
        /// Analyze browser distribution
        /// </summary>
        private BrowserAnalysis AnalyzeBrowsers(List<string> userAgents)
        {
            var analysis = new BrowserAnalysis();
            var browserCounts = new Dictionary<string, int>();

            foreach (var userAgent in userAgents)
            {
                var browser = IdentifyBrowser(userAgent);
                browserCounts[browser] = browserCounts.GetValueOrDefault(browser, 0) + 1;
            }

            analysis.BrowserDistribution = browserCounts;
            
            var total = userAgents.Count;
            analysis.ChromePercentage = (double)browserCounts.GetValueOrDefault("Chrome", 0) / total * 100;
            analysis.FirefoxPercentage = (double)browserCounts.GetValueOrDefault("Firefox", 0) / total * 100;
            analysis.SafariPercentage = (double)browserCounts.GetValueOrDefault("Safari", 0) / total * 100;
            analysis.EdgePercentage = (double)browserCounts.GetValueOrDefault("Edge", 0) / total * 100;
            analysis.OtherPercentage = 100 - analysis.ChromePercentage - analysis.FirefoxPercentage - 
                                     analysis.SafariPercentage - analysis.EdgePercentage;

            analysis.MostPopularBrowser = browserCounts.OrderByDescending(kvp => kvp.Value).FirstOrDefault().Key ?? "Unknown";

            return analysis;
        }

        /// <summary>
        /// Analyze device types
        /// </summary>
        private DeviceAnalysis AnalyzeDevices(List<string> userAgents)
        {
            var analysis = new DeviceAnalysis();
            var deviceCounts = new Dictionary<string, int>();

            foreach (var userAgent in userAgents)
            {
                var device = IdentifyDevice(userAgent);
                deviceCounts[device] = deviceCounts.GetValueOrDefault(device, 0) + 1;
            }

            analysis.DeviceTypes = deviceCounts;
            
            var total = userAgents.Count;
            analysis.DesktopPercentage = (double)deviceCounts.GetValueOrDefault("Desktop", 0) / total * 100;
            analysis.MobilePercentage = (double)deviceCounts.GetValueOrDefault("Mobile", 0) / total * 100;
            analysis.TabletPercentage = (double)deviceCounts.GetValueOrDefault("Tablet", 0) / total * 100;
            analysis.BotPercentage = (double)deviceCounts.GetValueOrDefault("Bot", 0) / total * 100;

            return analysis;
        }

        /// <summary>
        /// Analyze operating systems
        /// </summary>
        private OperatingSystemAnalysis AnalyzeOperatingSystems(List<string> userAgents)
        {
            var analysis = new OperatingSystemAnalysis();
            var osCounts = new Dictionary<string, int>();

            foreach (var userAgent in userAgents)
            {
                var os = IdentifyOperatingSystem(userAgent);
                osCounts[os] = osCounts.GetValueOrDefault(os, 0) + 1;
            }

            analysis.OSDistribution = osCounts;
            
            var total = userAgents.Count;
            analysis.WindowsPercentage = (double)osCounts.GetValueOrDefault("Windows", 0) / total * 100;
            analysis.MacOSPercentage = (double)osCounts.GetValueOrDefault("macOS", 0) / total * 100;
            analysis.LinuxPercentage = (double)osCounts.GetValueOrDefault("Linux", 0) / total * 100;
            analysis.AndroidPercentage = (double)osCounts.GetValueOrDefault("Android", 0) / total * 100;
            analysis.iOSPercentage = (double)osCounts.GetValueOrDefault("iOS", 0) / total * 100;
            analysis.OtherPercentage = 100 - analysis.WindowsPercentage - analysis.MacOSPercentage - 
                                     analysis.LinuxPercentage - analysis.AndroidPercentage - analysis.iOSPercentage;

            return analysis;
        }

        /// <summary>
        /// Initialize browser detection patterns
        /// </summary>
        private Dictionary<string, string> InitializeBrowserPatterns()
        {
            return new Dictionary<string, string>
            {
                ["Chrome"] = @"Chrome/[\d\.]+",
                ["Firefox"] = @"Firefox/[\d\.]+",
                ["Safari"] = @"Safari/[\d\.]+",
                ["Edge"] = @"Edg/[\d\.]+|Edge/[\d\.]+",
                ["Internet Explorer"] = @"MSIE [\d\.]+|Trident/[\d\.]+",
                ["Opera"] = @"Opera/[\d\.]+|OPR/[\d\.]+",
                ["Brave"] = @"Brave/[\d\.]+",
                ["Vivaldi"] = @"Vivaldi/[\d\.]+",
                ["Samsung Browser"] = @"SamsungBrowser/[\d\.]+",
                ["UC Browser"] = @"UCBrowser/[\d\.]+",
                ["Yandex"] = @"YaBrowser/[\d\.]+",
                ["DuckDuckGo"] = @"DuckDuckGo/[\d\.]+",
                ["Tor Browser"] = @"Tor Browser/[\d\.]+",
                ["Lynx"] = @"Lynx/[\d\.]+",
                ["curl"] = @"curl/[\d\.]+",
                ["wget"] = @"Wget/[\d\.]+",
                ["Postman"] = @"PostmanRuntime/[\d\.]+",
                ["Insomnia"] = @"insomnia/[\d\.]+",
                ["HTTPie"] = @"HTTPie/[\d\.]+",
                ["Python"] = @"python-requests/[\d\.]+|Python-urllib/[\d\.]+",
                ["Java"] = @"Java/[\d\.]+",
                ["Go"] = @"Go-http-client/[\d\.]+",
                ["Ruby"] = @"Ruby/[\d\.]+",
                ["PHP"] = @"PHP/[\d\.]+",
                ["Node.js"] = @"Node\.js/[\d\.]+",
                ["Axios"] = @"axios/[\d\.]+",
                ["Fetch"] = @"node-fetch/[\d\.]+",
                ["Bot"] = @"bot|crawler|spider|scraper|monitor|check|test|scan|index|search|archive|feed|reader|validator|preview|thumbnail|screenshot|pdf|image|video|audio|download|backup|sync|update|install|deploy|build|ci|cd|automation|script|tool|utility|service|daemon|worker|job|task|queue|batch|bulk|mass|auto|robot|machine|artificial|intelligence|ai|ml|deep|learning|neural|network|algorithm|data|mining|analysis|analytics|statistics|metrics|monitoring|tracking|logging|reporting|dashboard|visualization|chart|graph|plot|map|geo|location|weather|news|social|media|facebook|twitter|instagram|linkedin|youtube|google|bing|yahoo|duckduckgo|baidu|yandex|amazon|apple|microsoft|mozilla|opera|samsung|huawei|xiaomi|oppo|vivo|realme|oneplus|lg|sony|htc|motorola|nokia|blackberry|palm|symbian|windows|macos|linux|android|ios|ubuntu|debian|centos|redhat|fedora|opensuse|arch|gentoo|freebsd|openbsd|netbsd|solaris|aix|hpux|irix|tru64|qnx|beos|haiku|amiga|atari|commodore|sinclair|amstrad|msx|zx|spectrum|c64|apple2|macintosh|lisa|newton|palm|pocket|wince|mobile|tablet|phone|smartphone|iphone|ipad|ipod|android|kindle|nook|kobo|surface|chromebook|laptop|desktop|server|workstation|mainframe|supercomputer|cluster|grid|cloud|aws|azure|gcp|alibaba|tencent|baidu|oracle|ibm|hp|dell|lenovo|asus|acer|msi|gigabyte|intel|amd|nvidia|qualcomm|mediatek|snapdragon|exynos|kirin|bionic|helio|unisoc|rockchip|allwinner|amlogic|broadcom|marvell|realtek|via|sis|ati|matrox|3dfx|voodoo|geforce|radeon|quadro|tesla|titan|rtx|gtx|rx|vega|navi|rdna|gcn|terascale|unified|shader|cuda|opencl|directx|opengl|vulkan|metal|mantle|glide|software|hardware|firmware|bios|uefi|bootloader|kernel|driver|library|framework|runtime|virtual|machine|emulator|simulator|container|docker|kubernetes|openshift|rancher|nomad|consul|vault|terraform|ansible|puppet|chef|saltstack|fabric|capistrano|jenkins|travis|circleci|gitlab|github|bitbucket|azure|devops|bamboo|teamcity|octopus|spinnaker|argo|flux|tekton|knative|istio|linkerd|envoy|nginx|apache|iis|tomcat|jetty|undertow|netty|vertx|akka|play|spring|django|flask|express|koa|fastify|hapi|restify|meteor|next|nuxt|gatsby|hugo|jekyll|hexo|gridsome|vuepress|docusaurus|gitbook|mkdocs|sphinx|doxygen|javadoc|rustdoc|godoc|pydoc|rdoc|yard|jsdoc|typedoc|esdoc|documentation|readme|changelog|license|contributing|code|conduct|security|privacy|terms|conditions|policy|agreement|disclaimer|notice|copyright|trademark|patent|intellectual|property|open|source|free|libre|gnu|gpl|lgpl|agpl|bsd|mit|apache|mozilla|creative|commons|public|domain|copyleft|permissive|restrictive|commercial|proprietary|closed|private|confidential|secret|classified|restricted|limited|exclusive|proprietary|patented|trademarked|copyrighted|licensed|unlicensed|authorized|unauthorized|legal|illegal|legitimate|illegitimate|valid|invalid|authentic|fake|genuine|counterfeit|original|copy|clone|fork|branch|merge|pull|push|commit|checkout|revert|reset|rebase|cherry|pick|squash|stash|tag|release|version|patch|hotfix|bugfix|feature|enhancement|improvement|optimization|refactor|cleanup|maintenance|update|upgrade|downgrade|migration|conversion|transformation|translation|localization|internationalization|globalization|accessibility|usability|performance|scalability|reliability|availability|durability|consistency|integrity|security|privacy|safety|quality|testing|debugging|profiling|monitoring|logging|tracing|auditing|compliance|governance|risk|management|disaster|recovery|backup|restore|archive|retention|deletion|purging|cleanup|garbage|collection|memory|management|resource|allocation|deallocation|optimization|tuning|configuration|customization|personalization|adaptation|modification|extension|plugin|addon|module|component|library|package|bundle|distribution|deployment|installation|setup|configuration|initialization|startup|shutdown|restart|reload|refresh|sync|synchronization|replication|mirroring|clustering|load|balancing|failover|redundancy|high|availability|fault|tolerance|error|handling|exception|recovery|retry|timeout|circuit|breaker|bulkhead|rate|limiting|throttling|queuing|buffering|caching|memoization|lazy|loading|eager|loading|prefetching|preloading|streaming|chunking|pagination|filtering|sorting|searching|indexing|ranking|scoring|weighting|prioritization|scheduling|routing|dispatching|forwarding|proxying|tunneling|bridging|gateway|firewall|load|balancer|reverse|proxy|cdn|cache|database|storage|filesystem|memory|disk|network|internet|intranet|extranet|vpn|lan|wan|wifi|ethernet|bluetooth|usb|serial|parallel|i2c|spi|uart|gpio|pwm|adc|dac|timer|counter|interrupt|dma|cpu|gpu|tpu|npu|dsp|fpga|asic|soc|mcu|embedded|iot|edge|fog|cloud|hybrid|multi|distributed|decentralized|centralized|federated|peer|mesh|star|ring|bus|tree|graph|network|topology|protocol|tcp|udp|http|https|ftp|sftp|ssh|telnet|smtp|pop3|imap|dns|dhcp|ntp|snmp|ldap|kerberos|oauth|saml|jwt|ssl|tls|ipsec|vpn|firewall|ids|ips|waf|ddos|mitigation|protection|defense|attack|vulnerability|exploit|malware|virus|trojan|worm|rootkit|spyware|adware|ransomware|phishing|spoofing|hijacking|injection|xss|csrf|sqli|rce|lfi|rfi|xxe|ssrf|deserialization|buffer|overflow|heap|spray|rop|jop|ret2libc|shellcode|payload|backdoor|webshell|reverse|shell|bind|shell|command|execution|privilege|escalation|lateral|movement|persistence|evasion|obfuscation|encryption|decryption|hashing|signing|verification|authentication|authorization|access|control|identity|management|single|sign|multi|factor|biometric|captcha|honeypot|sandbox|quarantine|isolation|containment|segmentation|micro|segmentation|zero|trust|least|privilege|defense|depth|security|design|threat|modeling|risk|assessment|penetration|testing|red|team|blue|purple|white|box|black|gray|hat|ethical|hacking|bug|bounty|responsible|disclosure|coordinated|vulnerability|cve|cvss|cwe|owasp|sans|nist|iso|pci|hipaa|gdpr|ccpa|sox|fisma|fedramp|common|criteria|fips|eal|assurance|certification|accreditation|audit|compliance|governance|policy|procedure|standard|guideline|best|practice|framework|methodology|process|workflow|lifecycle|sdlc|devops|devsecops|agile|scrum|kanban|lean|waterfall|spiral|iterative|incremental|prototype|mvp|poc|pilot|beta|alpha|rc|ga|lts|eol|esr|stable|unstable|experimental|preview|snapshot|nightly|daily|weekly|monthly|quarterly|annually|continuous|integration|delivery|deployment|monitoring|feedback|improvement|innovation|research|development|design|architecture|engineering|programming|coding|scripting|markup|styling|testing|debugging|documentation|training|education|learning|teaching|mentoring|coaching|consulting|support|maintenance|operations|administration|management|leadership|strategy|planning|execution|delivery|quality|assurance|control|improvement|optimization|automation|orchestration|provisioning|configuration|deployment|scaling|monitoring|alerting|logging|tracing|profiling|debugging|troubleshooting|incident|response|problem|resolution|change|management|release|management|capacity|planning|performance|tuning|cost|optimization|resource|utilization|efficiency|productivity|collaboration|communication|coordination|synchronization|alignment|integration|interoperability|compatibility|portability|migration|transformation|modernization|digital|transformation|cloud|native|serverless|microservices|containers|kubernetes|docker|service|mesh|api|gateway|event|driven|reactive|functional|object|oriented|procedural|declarative|imperative|synchronous|asynchronous|blocking|non|concurrent|parallel|distributed|scalable|resilient|fault|tolerant|self|healing|adaptive|intelligent|machine|learning|artificial|intelligence|deep|neural|network|natural|language|processing|computer|vision|speech|recognition|synthesis|generation|recommendation|prediction|classification|clustering|regression|optimization|search|planning|reasoning|knowledge|representation|expert|system|decision|support|business|intelligence|data|science|analytics|visualization|dashboard|report|kpi|metric|measurement|monitoring|tracking|logging|auditing|compliance|governance|risk|management|security|privacy|safety|quality|performance|reliability|availability|scalability|usability|accessibility|maintainability|testability|deployability|operability|supportability|sustainability|efficiency|effectiveness|productivity|profitability|competitiveness|innovation|agility|flexibility|adaptability|responsiveness|resilience|robustness|stability|consistency|predictability|transparency|accountability|responsibility|ethics|trust|reputation|brand|value|proposition|customer|satisfaction|user|experience|employee|engagement|stakeholder|value|shareholder|return|investment|total|cost|ownership|return|assets|equity|debt|cash|flow|revenue|profit|margin|growth|market|share|competitive|advantage|differentiation|positioning|strategy|vision|mission|values|culture|leadership|governance|organization|structure|process|people|technology|data|information|knowledge|wisdom|insight|intelligence|understanding|awareness|consciousness|perception|cognition|emotion|intuition|creativity|imagination|inspiration|motivation|passion|purpose|meaning|fulfillment|happiness|well|being|health|wealth|success|achievement|accomplishment|recognition|appreciation|gratitude|respect|trust|love|friendship|relationship|community|society|humanity|world|universe|existence|reality|truth|beauty|goodness|justice|freedom|equality|diversity|inclusion|sustainability|responsibility|stewardship|legacy|future|generations|children|family|friends|colleagues|partners|customers|users|stakeholders|shareholders|investors|employees|management|leadership|board|directors|advisors|mentors|coaches|consultants|vendors|suppliers|contractors|freelancers|agencies|organizations|institutions|governments|regulators|standards|bodies|industry|associations|professional|societies|academic|research|educational|training|certification|accreditation|licensing|registration|membership|subscription|partnership|alliance|joint|venture|merger|acquisition|divestiture|spin|off|ipo|private|equity|venture|capital|angel|investor|crowdfunding|bootstrapping|self|funded|revenue|based|financing|debt|equity|hybrid|convertible|preferred|common|stock|option|warrant|bond|loan|credit|line|mortgage|lease|rental|subscription|license|franchise|royalty|commission|fee|salary|wage|bonus|incentive|benefit|compensation|package|total|rewards|performance|management|talent|acquisition|retention|development|succession|planning|career|path|progression|promotion|lateral|move|transfer|rotation|assignment|project|task|responsibility|accountability|authority|delegation|empowerment|autonomy|independence|collaboration|teamwork|communication|coordination|cooperation|conflict|resolution|negotiation|mediation|arbitration|litigation|settlement|agreement|contract|terms|conditions|service|level|agreement|sla|kpi|oki|metric|measurement|monitoring|reporting|dashboard|scorecard|balanced|scorecard|performance|indicator|key|result|objective|goal|target|milestone|deliverable|outcome|output|input|process|activity|task|step|phase|stage|gate|checkpoint|review|approval|sign|off|acceptance|criteria|definition|done|ready|backlog|sprint|iteration|release|version|build|deployment|environment|development|testing|staging|production|sandbox|demo|training|documentation|user|manual|admin|guide|api|reference|tutorial|walkthrough|quickstart|getting|started|installation|setup|configuration|troubleshooting|faq|help|support|contact|feedback|suggestion|feature|request|bug|report|issue|ticket|incident|problem|change|request|enhancement|improvement|optimization|refactoring|technical|debt|code|review|pair|programming|mob|programming|test|driven|development|behavior|driven|development|acceptance|test|driven|development|continuous|integration|continuous|delivery|continuous|deployment|continuous|monitoring|continuous|improvement|continuous|learning|continuous|feedback|continuous|experimentation|continuous|innovation|lean|startup|design|thinking|human|centered|design|user|experience|design|user|interface|design|interaction|design|visual|design|graphic|design|web|design|mobile|design|responsive|design|adaptive|design|progressive|web|app|single|page|application|multi|page|application|server|side|rendering|client|side|rendering|static|site|generation|jamstack|headless|cms|content|management|system|customer|relationship|management|enterprise|resource|planning|supply|chain|management|human|resource|management|financial|management|project|management|portfolio|management|program|management|product|management|service|management|asset|management|risk|management|compliance|management|governance|management|change|management|configuration|management|incident|management|problem|management|knowledge|management|document|management|content|management|digital|asset|management|brand|management|reputation|management|crisis|management|disaster|recovery|business|continuity|emergency|response|security|incident|response|cyber|security|information|security|network|security|application|security|data|security|cloud|security|mobile|security|iot|security|operational|security|physical|security|personnel|security|administrative|security|technical|security|preventive|detective|corrective|compensating|control|access|control|identity|access|management|privileged|access|management|single|sign|on|multi|factor|authentication|risk|based|authentication|adaptive|authentication|passwordless|authentication|biometric|authentication|certificate|based|authentication|token|based|authentication|api|key|oauth|openid|connect|saml|ldap|active|directory|kerberos|radius|tacacs|diameter|x509|pki|ca|certificate|authority|digital|certificate|ssl|tls|https|vpn|ipsec|wireguard|openvpn|firewall|next|generation|firewall|web|application|firewall|network|access|control|intrusion|detection|system|intrusion|prevention|system|security|information|event|management|security|orchestration|automation|response|endpoint|detection|response|extended|detection|response|managed|detection|response|threat|intelligence|threat|hunting|vulnerability|management|patch|management|configuration|management|compliance|monitoring|security|awareness|training|phishing|simulation|red|team|exercise|penetration|testing|vulnerability|assessment|security|audit|risk|assessment|business|impact|analysis|threat|modeling|attack|surface|management|zero|trust|architecture|software|defined|perimeter|cloud|access|security|broker|secure|web|gateway|email|security|gateway|data|loss|prevention|digital|rights|management|information|rights|management|database|activity|monitoring|file|integrity|monitoring|log|management|security|analytics|user|entity|behavior|analytics|network|traffic|analysis|malware|analysis|forensics|incident|response|digital|forensics|memory|forensics|network|forensics|mobile|forensics|cloud|forensics|threat|attribution|cyber|threat|intelligence|indicators|compromise|tactics|techniques|procedures|mitre|att|ck|kill|chain|diamond|model|pyramid|pain|cyber|security|framework|nist|cybersecurity|framework|iso|27001|cobit|itil|togaf|zachman|framework|enterprise|architecture|solution|architecture|technical|architecture|application|architecture|data|architecture|infrastructure|architecture|security|architecture|network|architecture|cloud|architecture|microservices|architecture|serverless|architecture|event|driven|architecture|service|oriented|architecture|rest|api|graphql|grpc|soap|xml|json|yaml|toml|ini|csv|tsv|excel|pdf|word|powerpoint|html|css|javascript|typescript|python|java|c|sharp|go|rust|kotlin|swift|php|ruby|perl|scala|clojure|haskell|erlang|elixir|f|sharp|r|matlab|sql|nosql|mongodb|postgresql|mysql|sqlite|redis|elasticsearch|cassandra|dynamodb|firebase|supabase|planetscale|neon|cockroachdb|yugabytedb|tidb|vitess|spanner|bigtable|bigquery|snowflake|databricks|spark|hadoop|kafka|rabbitmq|activemq|zeromq|nats|pulsar|kinesis|eventbridge|sns|sqs|lambda|azure|functions|google|cloud|functions|cloudflare|workers|vercel|netlify|heroku|digitalocean|linode|vultr|aws|ec2|s3|rds|dynamodb|lambda|api|gateway|cloudfront|route53|iam|vpc|security|groups|nacl|elb|alb|nlb|auto|scaling|cloudformation|terraform|ansible|puppet|chef|saltstack|kubernetes|docker|containerd|cri|o|podman|buildah|skopeo|helm|kustomize|istio|linkerd|envoy|nginx|ingress|controller|cert|manager|external|dns|cluster|autoscaler|horizontal|pod|autoscaler|vertical|pod|autoscaler|prometheus|grafana|jaeger|zipkin|opentelemetry|fluentd|fluent|bit|logstash|elasticsearch|kibana|splunk|datadog|new|relic|dynatrace|appdynamics|pingdom|uptime|robot|statuspage|pagerduty|opsgenie|victorops|xmatters|slack|microsoft|teams|discord|telegram|whatsapp|signal|zoom|google|meet|webex|skype|gotomeeting|bluejeans|jitsi|big|blue|button|moodle|canvas|blackboard|schoology|google|classroom|microsoft|365|office|365|sharepoint|onedrive|dropbox|box|google|drive|icloud|amazon|drive|mega|sync|com|pcloud|tresorit|spideroak|backblaze|carbonite|crashplan|acronis|veeam|commvault|veritas|netbackup|backup|exec|arcserve|bacula|amanda|duplicati|restic|borg|backup|rclone|rsync|robocopy|xcopy|tar|zip|rar|7zip|winzip|winrar|peazip|bandizip|keka|the|unarchiver|stuffit|expander|betterzip|archive|utility|file|compression|data|compression|lossless|compression|lossy|compression|image|compression|video|compression|audio|compression|text|compression|database|compression|backup|compression|network|compression|real|time|compression|streaming|compression|adaptive|compression|predictive|compression|dictionary|compression|entropy|compression|huffman|coding|arithmetic|coding|lempel|ziv|welch|deflate|gzip|bzip2|xz|lzma|lz4|zstd|snappy|brotli|webp|avif|heif|jpeg|png|gif|svg|tiff|bmp|ico|webm|mp4|avi|mov|wmv|flv|mkv|ogv|3gp|m4v|mpg|mpeg|vob|ts|m2ts|mts|mxf|f4v|asf|rm|rmvb|divx|xvid|h264|h265|hevc|av1|vp8|vp9|theora|dirac|prores|dnxhd|cineform|blackmagic|raw|red|raw|arri|raw|canon|raw|nikon|raw|sony|raw|panasonic|raw|fujifilm|raw|olympus|raw|pentax|raw|leica|raw|hasselblad|raw|phase|one|raw|mamiya|raw|contax|raw|bronica|raw|rollei|raw|voigtlander|raw|zeiss|raw|schneider|raw|rodenstock|raw|cooke|raw|angenieux|raw|fujinon|raw|canon|lens|nikon|lens|sony|lens|sigma|lens|tamron|lens|tokina|lens|samyang|lens|rokinon|lens|laowa|lens|venus|optics|lens|meyer|optik|lens|helios|lens|jupiter|lens|industar|lens|mir|lens|tair|lens|zenitar|lens|pentacon|lens|carl|zeiss|lens|leica|lens|voigtlander|lens|cosina|lens|konica|lens|minolta|lens|pentax|lens|ricoh|lens|olympus|lens|panasonic|lens|fujifilm|lens|mamiya|lens|contax|lens|yashica|lens|bronica|lens|rollei|lens|hasselblad|lens|phase|one|lens|leaf|lens|sinar|lens|linhof|lens|toyo|lens|wista|lens|ebony|lens|chamonix|lens|intrepid|lens|walker|lens|gibellini|lens|gandolfi|lens|horseman|lens|cambo|lens|arca|swiss|lens|gitzo|lens|manfrotto|lens|benro|lens|sirui|lens|really|right|stuff|lens|kirk|lens|wimberley|lens|jobu|lens|promedia|gear|lens|novoflex|lens|arca|swiss|tripod|gitzo|tripod|manfrotto|tripod|benro|tripod|sirui|tripod|vanguard|tripod|induro|tripod|feisol|tripod|really|right|stuff|tripod|kirk|tripod|wimberley|tripod|jobu|tripod|promedia|gear|tripod|novoflex|tripod|carbon|fiber|tripod|aluminum|tripod|travel|tripod|studio|tripod|video|tripod|photo|tripod|macro|tripod|telephoto|tripod|panoramic|tripod|ball|head|pan|tilt|head|gimbal|head|fluid|head|friction|head|gear|head|panoramic|head|macro|head|telephoto|head|video|head|photo|head|studio|head|field|head|travel|head|compact|head|lightweight|head|heavy|duty|head|professional|head|amateur|head|beginner|head|advanced|head|expert|head|master|head|camera|dslr|mirrorless|point|shoot|compact|medium|format|large|format|instant|film|digital|analog|vintage|modern|professional|amateur|beginner|advanced|expert|master|photographer|videographer|cinematographer|filmmaker|director|producer|editor|colorist|sound|engineer|composer|musician|artist|designer|developer|programmer|engineer|architect|manager|leader|executive|entrepreneur|founder|ceo|cto|cfo|coo|cmo|chro|ciso|cdo|cpo|cro|vp|svp|evp|president|chairman|board|member|advisor|consultant|freelancer|contractor|employee|intern|student|teacher|professor|researcher|scientist|analyst|specialist|expert|professional|practitioner|enthusiast|hobbyist|amateur|beginner|novice|learner|trainee|apprentice|mentee|protege|successor|heir|legacy|tradition|heritage|culture|history|story|narrative|journey|adventure|experience|memory|moment|time|place|space|location|destination|travel|trip|vacation|holiday|break|rest|relaxation|recreation|entertainment|fun|joy|happiness|pleasure|satisfaction|fulfillment|achievement|success|victory|win|triumph|celebration|party|festival|event|occasion|ceremony|ritual|tradition|custom|practice|habit|routine|schedule|plan|goal|objective|target|aim|purpose|mission|vision|dream|aspiration|hope|wish|desire|want|need|requirement|specification|criteria|standard|benchmark|metric|measure|indicator|signal|sign|symbol|icon|logo|brand|identity|image|reputation|perception|impression|opinion|view|perspective|angle|point|view|standpoint|position|stance|attitude|approach|method|technique|strategy|tactic|plan|scheme|design|blueprint|roadmap|path|route|way|direction|course|journey|process|procedure|workflow|pipeline|funnel|framework|structure|system|model|pattern|template|format|layout|design|style|theme|concept|idea|thought|notion|theory|hypothesis|assumption|belief|opinion|view|perspective|insight|understanding|knowledge|wisdom|intelligence|awareness|consciousness|mindfulness|attention|focus|concentration|meditation|reflection|contemplation|introspection|self|awareness|self|knowledge|self|understanding|self|improvement|self|development|personal|growth|professional|development|career|advancement|skill|building|learning|education|training|coaching|mentoring|guidance|support|help|assistance|service|care|compassion|empathy|sympathy|understanding|patience|tolerance|acceptance|forgiveness|love|kindness|generosity|charity|giving|sharing|collaboration|cooperation|teamwork|partnership|alliance|friendship|relationship|connection|bond|tie|link|association|affiliation|membership|belonging|community|society|culture|civilization|humanity|world|earth|planet|universe|cosmos|existence|life|being|consciousness|spirit|soul|mind|body|heart|emotion|feeling|sensation|perception|experience|reality|truth|fact|evidence|proof|data|information|knowledge|wisdom|insight|understanding|awareness|consciousness|mindfulness|presence|moment|now|here|today|present|current|actual|real|genuine|authentic|true|honest|sincere|transparent|open|clear|direct|straightforward|simple|plain|obvious|evident|apparent|visible|seen|observed|noticed|detected|discovered|found|identified|recognized|acknowledged|accepted|understood|comprehended|grasped|realized|appreciated|valued|treasured|cherished|loved|adored|admired|respected|honored|revered|worshipped|praised|celebrated|commemorated|remembered|memorialized|immortalized|eternal|everlasting|permanent|lasting|enduring|durable|stable|solid|strong|robust|resilient|tough|hard|firm|secure|safe|protected|defended|guarded|shielded|covered|hidden|concealed|secret|private|confidential|classified|restricted|limited|exclusive|special|unique|rare|precious|valuable|priceless|invaluable|irreplaceable|indispensable|essential|vital|critical|important|significant|meaningful|purposeful|useful|helpful|beneficial|advantageous|favorable|positive|good|great|excellent|outstanding|exceptional|extraordinary|remarkable|amazing|incredible|fantastic|wonderful|marvelous|magnificent|splendid|superb|brilliant|genius|masterful|skillful|talented|gifted|capable|competent|proficient|expert|professional|experienced|seasoned|veteran|mature|wise|intelligent|smart|clever|bright|sharp|quick|fast|rapid|swift|speedy|efficient|effective|productive|successful|victorious|winning|triumphant|champion|leader|pioneer|innovator|creator|inventor|designer|architect|builder|maker|producer|manufacturer|developer|programmer|engineer|scientist|researcher|analyst|consultant|advisor|mentor|coach|teacher|instructor|trainer|educator|professor|scholar|academic|intellectual|thinker|philosopher|theorist|visionary|dreamer|idealist|optimist|pessimist|realist|pragmatist|perfectionist|minimalist|maximalist|generalist|specialist|expert|novice|beginner|amateur|professional|hobbyist|enthusiast|fan|supporter|advocate|champion|defender|protector|guardian|keeper|custodian|steward|caretaker|manager|administrator|operator|user|customer|client|patient|student|member|participant|contributor|volunteer|donor|sponsor|patron|benefactor|investor|shareholder|stakeholder|partner|ally|friend|colleague|teammate|collaborator|competitor|rival|opponent|enemy|adversary|challenger|contender|candidate|applicant|nominee|winner|loser|survivor|victim|hero|villain|protagonist|antagonist|character|person|individual|human|being|creature|entity|object|thing|item|element|component|part|piece|fragment|segment|section|portion|share|fraction|percentage|ratio|proportion|scale|size|dimension|measurement|quantity|amount|number|count|total|sum|aggregate|collection|group|set|series|sequence|order|arrangement|organization|structure|system|network|web|grid|matrix|table|list|array|vector|tree|graph|chart|diagram|map|model|representation|illustration|image|picture|photo|drawing|sketch|painting|artwork|design|pattern|template|format|layout|style|theme|concept|idea|thought|notion|theory|hypothesis|assumption|belief|opinion|view|perspective|insight|understanding|knowledge|wisdom|intelligence|awareness|consciousness|mindfulness|attention|focus|concentration|meditation|reflection|contemplation|introspection|self|awareness|self|knowledge|self|understanding|self|improvement|self|development|personal|growth|professional|development|career|advancement|skill|building|learning|education|training|coaching|mentoring|guidance|support|help|assistance|service|care|compassion|empathy|sympathy|understanding|patience|tolerance|acceptance|forgiveness|love|kindness|generosity|charity|giving|sharing|collaboration|cooperation|teamwork|partnership|alliance|friendship|relationship|connection|bond|tie|link|association|affiliation|membership|belonging|community|society|culture|civilization|humanity|world|earth|planet|universe|cosmos|existence|life|being|consciousness|spirit|soul|mind|body|heart|emotion|feeling|sensation|perception|experience|reality|truth|fact|evidence|proof|data|information|knowledge|wisdom|insight|understanding|awareness|consciousness|mindfulness|presence|moment|now|here|today|present|current|actual|real|genuine|authentic|true|honest|sincere|transparent|open|clear|direct|straightforward|simple|plain|obvious|evident|apparent|visible|seen|observed|noticed|detected|discovered|found|identified|recognized|acknowledged|accepted|understood|comprehended|grasped|realized|appreciated|valued|treasured|cherished|loved|adored|admired|respected|honored|revered|worshipped|praised|celebrated|commemorated|remembered|memorialized|immortalized|eternal|everlasting|permanent|lasting|enduring|durable|stable|solid|strong|robust|resilient|tough|hard|firm|secure|safe|protected|defended|guarded|shielded|covered|hidden|concealed|secret|private|confidential|classified|restricted|limited|exclusive|special|unique|rare|precious|valuable|priceless|invaluable|irreplaceable|indispensable|essential|vital|critical|important|significant|meaningful|purposeful|useful|helpful|beneficial|advantageous|favorable|positive|good|great|excellent|outstanding|exceptional|extraordinary|remarkable|amazing|incredible|fantastic|wonderful|marvelous|magnificent|splendid|superb|brilliant|genius|masterful|skillful|talented|gifted|capable|competent|proficient|expert|professional|experienced|seasoned|veteran|mature|wise|intelligent|smart|clever|bright|sharp|quick|fast|rapid|swift|speedy|efficient|effective|productive|successful|victorious|winning|triumphant|champion|leader|pioneer|innovator|creator|inventor|designer|architect|builder|maker|producer|manufacturer|developer|programmer|engineer|scientist|researcher|analyst|consultant|advisor|mentor|coach|teacher|instructor|trainer|educator|professor|scholar|academic|intellectual|thinker|philosopher|theorist|visionary|dreamer|idealist|optimist|pessimist|realist|pragmatist|perfectionist|minimalist|maximalist|generalist|specialist|expert|novice|beginner|amateur|professional|hobbyist|enthusiast|fan|supporter|advocate|champion|defender|protector|guardian|keeper|custodian|steward|caretaker|manager|administrator|operator|user|customer|client|patient|student|member|participant|contributor|volunteer|donor|sponsor|patron|benefactor|investor|shareholder|stakeholder|partner|ally|friend|colleague|teammate|collaborator|competitor|rival|opponent|enemy|adversary|challenger|contender|candidate|applicant|nominee|winner|loser|survivor|victim|hero|villain|protagonist|antagonist|character|person|individual|human|being|creature|entity|object|thing|item|element|component|part|piece|fragment|segment|section|portion|share|fraction|percentage|ratio|proportion|scale|size|dimension|measurement|quantity|amount|number|count|total|sum|aggregate|collection|group|set|series|sequence|order|arrangement|organization|structure|system|network|web|grid|matrix|table|list|array|vector|tree|graph|chart|diagram|map|model|representation|illustration|image|picture|photo|drawing|sketch|painting|artwork|design|pattern|template|format|layout|style|theme|concept|idea|thought|notion|theory|hypothesis|assumption|belief|opinion|view|perspective|insight|understanding|knowledge|wisdom|intelligence|awareness|consciousness|mindfulness|attention|focus|concentration|meditation|reflection|contemplation|introspection"
            };
        }

        /// <summary>
        /// Initialize OS detection patterns
        /// </summary>
        private Dictionary<string, string> InitializeOSPatterns()
        {
            return new Dictionary<string, string>
            {
                ["Windows"] = @"Windows NT [\d\.]+|Windows [\d\.]+|Win32|Win64|WOW64",
                ["macOS"] = @"Mac OS X [\d_\.]+|macOS [\d\.]+|Darwin/[\d\.]+",
                ["iOS"] = @"iPhone OS [\d_\.]+|iOS [\d\.]+|iPad|iPhone|iPod",
                ["Android"] = @"Android [\d\.]+",
                ["Linux"] = @"Linux|Ubuntu|Debian|CentOS|RedHat|Fedora|SUSE|Arch",
                ["Chrome OS"] = @"CrOS",
                ["FreeBSD"] = @"FreeBSD",
                ["OpenBSD"] = @"OpenBSD",
                ["NetBSD"] = @"NetBSD",
                ["Solaris"] = @"SunOS|Solaris",
                ["AIX"] = @"AIX",
                ["HP-UX"] = @"HP-UX",
                ["QNX"] = @"QNX",
                ["BeOS"] = @"BeOS|Haiku",
                ["OS/2"] = @"OS/2",
                ["AmigaOS"] = @"AmigaOS|MorphOS|AROS"
            };
        }

        /// <summary>
        /// Initialize device detection patterns
        /// </summary>
        private Dictionary<string, string> InitializeDevicePatterns()
        {
            return new Dictionary<string, string>
            {
                ["Mobile"] = @"Mobile|iPhone|iPod|Android|BlackBerry|Windows Phone|webOS|Opera Mini|Opera Mobi|IEMobile",
                ["Tablet"] = @"iPad|Android.*Tablet|Kindle|Silk|PlayBook|BB10.*Touch",
                ["Desktop"] = @"Windows NT|Mac OS X|Linux|FreeBSD|OpenBSD|NetBSD|SunOS|AIX|HP-UX",
                ["Bot"] = @"bot|crawler|spider|scraper|monitor|check|test|scan|index|search|archive|feed|reader|validator|preview|thumbnail|screenshot|pdf|image|video|audio|download|backup|sync|update|install|deploy|build|ci|cd|automation|script|tool|utility|service|daemon|worker|job|task|queue|batch|bulk|mass|auto|robot|machine|artificial|intelligence|ai|ml|deep|learning|neural|network|algorithm|data|mining|analysis|analytics|statistics|metrics|monitoring|tracking|logging|reporting|dashboard|visualization|chart|graph|plot|map|geo|location|weather|news|social|media",
                ["Smart TV"] = @"Smart-TV|SmartTV|GoogleTV|AppleTV|Roku|Chromecast|Fire TV|Android TV|webOS|Tizen",
                ["Game Console"] = @"PlayStation|Xbox|Nintendo|Wii|Switch|3DS|PSP|PS Vita",
                ["E-Reader"] = @"Kindle|Nook|Kobo|Sony Reader",
                ["Smartwatch"] = @"Apple Watch|Wear OS|Tizen|watchOS",
                ["IoT Device"] = @"IoT|Smart|Connected|Alexa|Google Home|Nest|Ring|Philips Hue|SmartThings"
            };
        }

        /// <summary>
        /// Determine user type based on behavior patterns
        /// </summary>
        private UserType DetermineUserType(List<IISLogEntry> entries)
        {
            var uniqueEndpoints = entries.Select(e => e.ApiEndpoint).Distinct().Count();
            var requestsPerHour = entries.Count / Math.Max(1, (entries.Last().DateTime - entries.First().DateTime).TotalHours);
            var errorRate = (double)entries.Count(e => !e.IsSuccessful) / entries.Count * 100;
            var userAgents = entries.Select(e => e.UserAgent).Distinct().ToList();

            // Check if it's a bot
            if (userAgents.Any(ua => IsBot(ua)))
                return UserType.Bot;

            // Check for scraper behavior
            if (uniqueEndpoints > 50 && requestsPerHour > 100)
                return UserType.Scraper;

            // Check for suspicious activity
            if (errorRate > 50 || (uniqueEndpoints > 20 && errorRate > 30))
                return UserType.Suspicious;

            // Check for power user
            if (requestsPerHour > 50 && uniqueEndpoints > 10)
                return UserType.PowerUser;

            // Check for developer patterns
            if (userAgents.Any(ua => IsDeveloperTool(ua)))
                return UserType.Developer;

            return UserType.RegularUser;
        }

        /// <summary>
        /// Analyze request patterns for an IP
        /// </summary>
        private List<RequestPattern> AnalyzeRequestPatterns(List<IISLogEntry> entries)
        {
            var patterns = new List<RequestPattern>();
            var endpointGroups = entries.GroupBy(e => e.ApiEndpoint);

            foreach (var group in endpointGroups.Where(g => g.Count() >= 3))
            {
                var times = group.Select(e => e.DateTime).OrderBy(t => t).ToList();
                var intervals = new List<TimeSpan>();

                for (int i = 1; i < times.Count; i++)
                {
                    intervals.Add(times[i] - times[i - 1]);
                }

                if (intervals.Any())
                {
                    var avgInterval = TimeSpan.FromTicks((long)intervals.Average(i => i.Ticks));
                    var isRegular = intervals.All(i => Math.Abs((i - avgInterval).TotalMinutes) < 5);

                    patterns.Add(new RequestPattern
                    {
                        Pattern = group.Key,
                        Frequency = group.Count(),
                        AverageInterval = avgInterval,
                        IsRegular = isRegular
                    });
                }
            }

            return patterns.OrderByDescending(p => p.Frequency).ToList();
        }

        /// <summary>
        /// Create user session from log entries
        /// </summary>
        private UserSession CreateUserSession(List<IISLogEntry> entries, string ip, string userAgent)
        {
            var session = new UserSession
            {
                IPAddress = ip,
                UserAgent = userAgent,
                StartTime = entries.First().DateTime,
                EndTime = entries.Last().DateTime,
                Duration = entries.Last().DateTime - entries.First().DateTime,
                PageViews = entries.Count,
                VisitedPages = entries.Select(e => e.ApiEndpoint).Distinct().ToList(),
                EntryPage = entries.First().ApiEndpoint,
                ExitPage = entries.Last().ApiEndpoint,
                BounceSession = entries.Select(e => e.ApiEndpoint).Distinct().Count() == 1,
                Referrer = entries.FirstOrDefault(e => !string.IsNullOrEmpty(e.Referer) && e.Referer != "-")?.Referer ?? ""
            };

            return session;
        }

        /// <summary>
        /// Calculate overall behavior metrics
        /// </summary>
        private BehaviorMetrics CalculateBehaviorMetrics(UserBehaviorAnalysis analysis, List<IISLogEntry> logEntries)
        {
            var metrics = new BehaviorMetrics
            {
                TotalUniqueIPs = analysis.MostActiveIPs.Count,
                TotalEstimatedSessions = analysis.EstimatedSessions.Count
            };

            if (analysis.EstimatedSessions.Any())
            {
                metrics.AverageSessionDuration = analysis.EstimatedSessions.Average(s => s.Duration.TotalMinutes);
                metrics.AveragePageViewsPerSession = analysis.EstimatedSessions.Average(s => s.PageViews);
                metrics.BounceRate = (double)analysis.EstimatedSessions.Count(s => s.BounceSession) / analysis.EstimatedSessions.Count * 100;

                // Session duration distribution
                metrics.SessionDurationDistribution = analysis.EstimatedSessions
                    .GroupBy(s => GetDurationBucket(s.Duration))
                    .ToDictionary(g => g.Key, g => g.Count());

                // Page views distribution
                metrics.PageViewsDistribution = analysis.EstimatedSessions
                    .GroupBy(s => GetPageViewsBucket(s.PageViews))
                    .ToDictionary(g => g.Key, g => g.Count());
            }

            // Estimate return vs new visitors (simplified)
            var ipFirstSeen = logEntries.GroupBy(e => e.ClientIP)
                .ToDictionary(g => g.Key, g => g.Min(e => e.DateTime));

            var timeSpan = logEntries.Max(e => e.DateTime) - logEntries.Min(e => e.DateTime);
            var midPoint = logEntries.Min(e => e.DateTime).Add(TimeSpan.FromTicks(timeSpan.Ticks / 2));

            metrics.NewVisitors = ipFirstSeen.Count(kvp => kvp.Value >= midPoint);
            metrics.ReturnVisitors = ipFirstSeen.Count - metrics.NewVisitors;
            metrics.ReturnVisitorPercentage = (double)metrics.ReturnVisitors / ipFirstSeen.Count * 100;

            return metrics;
        }

        // Additional helper methods for pattern matching and categorization...
        private string IdentifyBrowser(string userAgent)
        {
            if (string.IsNullOrEmpty(userAgent)) return "Unknown";

            foreach (var pattern in _browserPatterns)
            {
                if (Regex.IsMatch(userAgent, pattern.Value, RegexOptions.IgnoreCase))
                    return pattern.Key;
            }

            return "Unknown";
        }

        private string IdentifyDevice(string userAgent)
        {
            if (string.IsNullOrEmpty(userAgent)) return "Unknown";

            foreach (var pattern in _devicePatterns)
            {
                if (Regex.IsMatch(userAgent, pattern.Value, RegexOptions.IgnoreCase))
                    return pattern.Key;
            }

            return "Desktop"; // Default assumption
        }

        private string IdentifyOperatingSystem(string userAgent)
        {
            if (string.IsNullOrEmpty(userAgent)) return "Unknown";

            foreach (var pattern in _osPatterns)
            {
                if (Regex.IsMatch(userAgent, pattern.Value, RegexOptions.IgnoreCase))
                    return pattern.Key;
            }

            return "Unknown";
        }

        private bool IsKnownUserAgent(string userAgent)
        {
            return IdentifyBrowser(userAgent) != "Unknown" ||
                   IdentifyDevice(userAgent) != "Unknown" ||
                   IdentifyOperatingSystem(userAgent) != "Unknown";
        }

        private bool IsBot(string userAgent)
        {
            return Regex.IsMatch(userAgent, _devicePatterns["Bot"], RegexOptions.IgnoreCase);
        }

        private bool IsDeveloperTool(string userAgent)
        {
            var devTools = new[] { "curl", "wget", "postman", "insomnia", "httpie", "python-requests", "java", "go-http-client" };
            return devTools.Any(tool => userAgent.ToLower().Contains(tool));
        }

        private string CategorizeReferrer(string referrer)
        {
            if (string.IsNullOrEmpty(referrer) || referrer == "-")
                return "Direct";

            var lowerReferrer = referrer.ToLower();

            if (lowerReferrer.Contains("google") || lowerReferrer.Contains("bing") || lowerReferrer.Contains("yahoo") ||
                lowerReferrer.Contains("duckduckgo") || lowerReferrer.Contains("baidu"))
                return "Search";

            if (lowerReferrer.Contains("facebook") || lowerReferrer.Contains("twitter") || lowerReferrer.Contains("instagram") ||
                lowerReferrer.Contains("linkedin") || lowerReferrer.Contains("youtube") || lowerReferrer.Contains("tiktok"))
                return "Social";

            return "Other";
        }

        private List<TrafficSource> CreateTrafficSources(ReferrerAnalysis analysis, List<IISLogEntry> logEntries)
        {
            var sources = new List<TrafficSource>();

            // Add search engines
            foreach (var search in analysis.SearchEngines.Take(5))
            {
                sources.Add(new TrafficSource
                {
                    Source = ExtractDomain(search.Key),
                    Category = "Search",
                    RequestCount = search.Value,
                    UniqueVisitors = logEntries.Count(e => e.Referer == search.Key),
                    Percentage = (double)search.Value / logEntries.Count * 100
                });
            }

            // Add social media
            foreach (var social in analysis.SocialMedia.Take(5))
            {
                sources.Add(new TrafficSource
                {
                    Source = ExtractDomain(social.Key),
                    Category = "Social",
                    RequestCount = social.Value,
                    UniqueVisitors = logEntries.Count(e => e.Referer == social.Key),
                    Percentage = (double)social.Value / logEntries.Count * 100
                });
            }

            return sources.OrderByDescending(s => s.RequestCount).ToList();
        }

        private string ExtractDomain(string url)
        {
            try
            {
                var uri = new Uri(url);
                return uri.Host;
            }
            catch
            {
                return url;
            }
        }

        private int GetDurationBucket(TimeSpan duration)
        {
            var minutes = (int)duration.TotalMinutes;
            if (minutes <= 1) return 1;
            if (minutes <= 5) return 5;
            if (minutes <= 15) return 15;
            if (minutes <= 30) return 30;
            if (minutes <= 60) return 60;
            return 999;
        }

        private int GetPageViewsBucket(int pageViews)
        {
            if (pageViews == 1) return 1;
            if (pageViews <= 5) return 5;
            if (pageViews <= 10) return 10;
            if (pageViews <= 20) return 20;
            if (pageViews <= 50) return 50;
            return 999;
        }
    }
}
