namespace IISLogAnalyzer.Forms
{
    partial class ChartsForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.tabControl = new TabControl();
            this.tabPageTopApis = new TabPage();
            this.tabPageStatusCodes = new TabPage();
            this.tabPageResponseTimes = new TabPage();
            this.tabPageMethods = new TabPage();
            this.tabPageSummary = new TabPage();
            this.dataGridViewTopApis = new DataGridView();
            this.dataGridViewStatusCodes = new DataGridView();
            this.dataGridViewResponseTimes = new DataGridView();
            this.dataGridViewMethods = new DataGridView();
            this.richTextBoxSummary = new RichTextBox();
            this.panelButtons = new Panel();
            this.btnRefresh = new Button();
            this.btnExportData = new Button();
            this.btnClose = new Button();

            this.tabControl.SuspendLayout();
            this.tabPageTopApis.SuspendLayout();
            this.tabPageStatusCodes.SuspendLayout();
            this.tabPageResponseTimes.SuspendLayout();
            this.tabPageMethods.SuspendLayout();
            this.tabPageSummary.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewTopApis)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewStatusCodes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewResponseTimes)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewMethods)).BeginInit();
            this.panelButtons.SuspendLayout();
            this.SuspendLayout();
            
            //
            // tabControl
            //
            this.tabControl.Controls.Add(this.tabPageTopApis);
            this.tabControl.Controls.Add(this.tabPageStatusCodes);

            this.tabControl.Controls.Add(this.tabPageResponseTimes);
            this.tabControl.Controls.Add(this.tabPageMethods);
            this.tabControl.Controls.Add(this.tabPageSummary);
            this.tabControl.Dock = DockStyle.Fill;
            this.tabControl.Location = new Point(0, 0);
            this.tabControl.Name = "tabControl";
            this.tabControl.SelectedIndex = 0;
            this.tabControl.Size = new Size(1200, 750);
            this.tabControl.TabIndex = 0;

            //
            // tabPageTopApis
            //
            this.tabPageTopApis.Controls.Add(this.dataGridViewTopApis);
            this.tabPageTopApis.Location = new Point(4, 24);
            this.tabPageTopApis.Name = "tabPageTopApis";
            this.tabPageTopApis.Padding = new Padding(3);
            this.tabPageTopApis.Size = new Size(1192, 722);
            this.tabPageTopApis.TabIndex = 0;
            this.tabPageTopApis.Text = "Top APIs";
            this.tabPageTopApis.UseVisualStyleBackColor = true;

            //
            // tabPageStatusCodes
            //
            this.tabPageStatusCodes.Controls.Add(this.dataGridViewStatusCodes);
            this.tabPageStatusCodes.Controls.Add(this.chartStatusCodes);
            this.tabPageStatusCodes.Location = new Point(4, 24);
            this.tabPageStatusCodes.Name = "tabPageStatusCodes";
            this.tabPageStatusCodes.Padding = new Padding(3);
            this.tabPageStatusCodes.Size = new Size(1192, 722);
            this.tabPageStatusCodes.TabIndex = 1;
            this.tabPageStatusCodes.Text = "Status Codes";
            this.tabPageStatusCodes.UseVisualStyleBackColor = true;

            //
            // chartStatusCodes
            //
            this.chartStatusCodes = new System.Windows.Forms.DataVisualization.Charting.Chart();
            ((System.ComponentModel.ISupportInitialize)(this.chartStatusCodes)).BeginInit();
            this.chartStatusCodes.Dock = System.Windows.Forms.DockStyle.Top;
            this.chartStatusCodes.Height = 300;
            this.chartStatusCodes.Location = new Point(3, 3);
            this.chartStatusCodes.Name = "chartStatusCodes";
            this.chartStatusCodes.Size = new Size(1186, 300);
            this.chartStatusCodes.TabIndex = 1;

            //
            // tabPageResponseTimes
            //
            this.tabPageResponseTimes.Controls.Add(this.dataGridViewResponseTimes);
            this.tabPageResponseTimes.Location = new Point(4, 24);
            this.tabPageResponseTimes.Name = "tabPageResponseTimes";
            this.tabPageResponseTimes.Padding = new Padding(3);
            this.tabPageResponseTimes.Size = new Size(1192, 722);
            this.tabPageResponseTimes.TabIndex = 2;
            this.tabPageResponseTimes.Text = "Response Times";
            this.tabPageResponseTimes.UseVisualStyleBackColor = true;

            //
            // tabPageMethods
            //
            this.tabPageMethods.Controls.Add(this.dataGridViewMethods);
            this.tabPageMethods.Location = new Point(4, 24);
            this.tabPageMethods.Name = "tabPageMethods";
            this.tabPageMethods.Padding = new Padding(3);
            this.tabPageMethods.Size = new Size(1192, 722);
            this.tabPageMethods.TabIndex = 3;
            this.tabPageMethods.Text = "HTTP Methods";
            this.tabPageMethods.UseVisualStyleBackColor = true;

            //
            // tabPageSummary
            //
            this.tabPageSummary.Controls.Add(this.richTextBoxSummary);
            this.tabPageSummary.Location = new Point(4, 24);
            this.tabPageSummary.Name = "tabPageSummary";
            this.tabPageSummary.Padding = new Padding(3);
            this.tabPageSummary.Size = new Size(1192, 722);
            this.tabPageSummary.TabIndex = 4;
            this.tabPageSummary.Text = "Summary";
            this.tabPageSummary.UseVisualStyleBackColor = true;
            
            //
            // dataGridViewTopApis
            //
            this.dataGridViewTopApis.AllowUserToAddRows = false;
            this.dataGridViewTopApis.AllowUserToDeleteRows = false;
            this.dataGridViewTopApis.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridViewTopApis.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewTopApis.Dock = DockStyle.Fill;
            this.dataGridViewTopApis.Location = new Point(3, 3);
            this.dataGridViewTopApis.Name = "dataGridViewTopApis";
            this.dataGridViewTopApis.ReadOnly = true;
            this.dataGridViewTopApis.RowHeadersVisible = false;
            this.dataGridViewTopApis.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewTopApis.Size = new Size(1186, 716);
            this.dataGridViewTopApis.TabIndex = 0;

            //
            // dataGridViewStatusCodes
            //
            this.dataGridViewStatusCodes.AllowUserToAddRows = false;
            this.dataGridViewStatusCodes.AllowUserToDeleteRows = false;
            this.dataGridViewStatusCodes.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridViewStatusCodes.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewStatusCodes.Dock = DockStyle.Fill;
            this.dataGridViewStatusCodes.Location = new Point(3, 303);
            this.dataGridViewStatusCodes.Name = "dataGridViewStatusCodes";
            this.dataGridViewStatusCodes.ReadOnly = true;
            this.dataGridViewStatusCodes.RowHeadersVisible = false;
            this.dataGridViewStatusCodes.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewStatusCodes.Size = new Size(1186, 416);
            this.dataGridViewStatusCodes.TabIndex = 0;

            //
            // dataGridViewResponseTimes
            //
            this.dataGridViewResponseTimes.AllowUserToAddRows = false;
            this.dataGridViewResponseTimes.AllowUserToDeleteRows = false;
            this.dataGridViewResponseTimes.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridViewResponseTimes.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewResponseTimes.Dock = DockStyle.Fill;
            this.dataGridViewResponseTimes.Location = new Point(3, 3);
            this.dataGridViewResponseTimes.Name = "dataGridViewResponseTimes";
            this.dataGridViewResponseTimes.ReadOnly = true;
            this.dataGridViewResponseTimes.RowHeadersVisible = false;
            this.dataGridViewResponseTimes.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewResponseTimes.Size = new Size(1186, 716);
            this.dataGridViewResponseTimes.TabIndex = 0;

            //
            // dataGridViewMethods
            //
            this.dataGridViewMethods.AllowUserToAddRows = false;
            this.dataGridViewMethods.AllowUserToDeleteRows = false;
            this.dataGridViewMethods.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            this.dataGridViewMethods.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dataGridViewMethods.Dock = DockStyle.Fill;
            this.dataGridViewMethods.Location = new Point(3, 3);
            this.dataGridViewMethods.Name = "dataGridViewMethods";
            this.dataGridViewMethods.ReadOnly = true;
            this.dataGridViewMethods.RowHeadersVisible = false;
            this.dataGridViewMethods.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            this.dataGridViewMethods.Size = new Size(1186, 716);
            this.dataGridViewMethods.TabIndex = 0;

            //
            // richTextBoxSummary
            //
            this.richTextBoxSummary.Dock = DockStyle.Fill;
            this.richTextBoxSummary.Font = new Font("Consolas", 9F, FontStyle.Regular, GraphicsUnit.Point);
            this.richTextBoxSummary.Location = new Point(3, 3);
            this.richTextBoxSummary.Name = "richTextBoxSummary";
            this.richTextBoxSummary.ReadOnly = true;
            this.richTextBoxSummary.Size = new Size(1186, 716);
            this.richTextBoxSummary.TabIndex = 0;
            
            // 
            // panelButtons
            // 
            this.panelButtons.Controls.Add(this.btnRefresh);
            this.panelButtons.Controls.Add(this.btnExportData);
            this.panelButtons.Controls.Add(this.btnClose);
            this.panelButtons.Dock = DockStyle.Bottom;
            this.panelButtons.Location = new Point(0, 750);
            this.panelButtons.Name = "panelButtons";
            this.panelButtons.Size = new Size(1200, 50);
            this.panelButtons.TabIndex = 1;
            
            // 
            // btnRefresh
            // 
            this.btnRefresh.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.btnRefresh.Location = new Point(930, 15);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new Size(80, 25);
            this.btnRefresh.TabIndex = 0;
            this.btnRefresh.Text = "Refresh";
            this.btnRefresh.UseVisualStyleBackColor = true;
            this.btnRefresh.Click += new EventHandler(this.btnRefresh_Click);
            
            //
            // btnExportData
            //
            this.btnExportData.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.btnExportData.Location = new Point(1020, 15);
            this.btnExportData.Name = "btnExportData";
            this.btnExportData.Size = new Size(80, 25);
            this.btnExportData.TabIndex = 1;
            this.btnExportData.Text = "Export";
            this.btnExportData.UseVisualStyleBackColor = true;
            this.btnExportData.Click += new EventHandler(this.btnExportData_Click);
            
            // 
            // btnClose
            // 
            this.btnClose.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
            this.btnClose.Location = new Point(1110, 15);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new Size(80, 25);
            this.btnClose.TabIndex = 2;
            this.btnClose.Text = "Close";
            this.btnClose.UseVisualStyleBackColor = true;
            this.btnClose.Click += new EventHandler(this.btnClose_Click);
            
            //
            // ChartsForm
            //
            this.AutoScaleDimensions = new SizeF(7F, 15F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.Controls.Add(this.tabControl);
            this.Controls.Add(this.panelButtons);
            this.Name = "ChartsForm";
            this.StartPosition = FormStartPosition.CenterParent;
            this.Text = "IIS Log Analysis - Data Visualizations";
            this.WindowState = FormWindowState.Maximized;

            this.tabControl.ResumeLayout(false);
            this.tabPageTopApis.ResumeLayout(false);
            this.tabPageStatusCodes.ResumeLayout(false);
            this.tabPageResponseTimes.ResumeLayout(false);
            this.tabPageMethods.ResumeLayout(false);
            this.tabPageSummary.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewTopApis)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewStatusCodes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewResponseTimes)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dataGridViewMethods)).EndInit();
            this.panelButtons.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chartStatusCodes)).EndInit();
            this.ResumeLayout(false);
        }

        #endregion

        private TabControl tabControl;
        private TabPage tabPageTopApis;
        private TabPage tabPageStatusCodes;
        private TabPage tabPageResponseTimes;
        private TabPage tabPageMethods;
        private TabPage tabPageSummary;
        private DataGridView dataGridViewTopApis;
        private DataGridView dataGridViewStatusCodes;
        private DataGridView dataGridViewResponseTimes;
        private DataGridView dataGridViewMethods;
        private RichTextBox richTextBoxSummary;
        private Panel panelButtons;
        private Button btnRefresh;
        private Button btnExportData;
        private Button btnClose;
        private System.Windows.Forms.DataVisualization.Charting.Chart chartStatusCodes;
    }
}
