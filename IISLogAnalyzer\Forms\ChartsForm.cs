using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Forms
{
    /// <summary>
    /// Form for displaying various visualizations of IIS log analysis data using simple controls
    /// </summary>
    public partial class ChartsForm : Form
    {
        private readonly OverallAnalysisSummary _analysisSummary;

        public ChartsForm(OverallAnalysisSummary analysisSummary)
        {
            _analysisSummary = analysisSummary ?? throw new ArgumentNullException(nameof(analysisSummary));
            InitializeComponent();
            PopulateVisualizationData();
        }

        private void PopulateVisualizationData()
        {
            PopulateTopApisGrid();
            PopulateStatusCodeGrid();
            PopulateStatusCodeChart();
            PopulateResponseTimeGrid();
            PopulateMethodDistributionGrid();
            PopulatePerformanceGrid();
            PopulateSecurityGrid();
            PopulateUserBehaviorGrid();
            PopulateErrorsGrid();
            PopulateApiUsageGrid();
            PopulateSummaryStats();
        }

        private void PopulateStatusCodeChart()
        {
            try
            {
                // Defensive: Check if chartStatusCodes is initialized
                if (chartStatusCodes == null)
                    return;

                // Defensive: Check if data is available
                if (_analysisSummary?.OverallStatusCodeDistribution == null || !_analysisSummary.OverallStatusCodeDistribution.Any())
                {
                    chartStatusCodes.Series.Clear();
                    chartStatusCodes.ChartAreas.Clear();
                    chartStatusCodes.Titles.Clear();
                    chartStatusCodes.Titles.Add("No status code data available");
                    return;
                }

                // Clear previous chart data
                chartStatusCodes.Series.Clear();
                chartStatusCodes.ChartAreas.Clear();
                chartStatusCodes.Titles.Clear();

                var chartArea = new ChartArea();
                chartStatusCodes.ChartAreas.Add(chartArea);

                var series = new Series
                {
                    Name = "StatusCodes",
                    ChartType = SeriesChartType.Pie,
                    IsValueShownAsLabel = true
                };

                var totalRequests = _analysisSummary.OverallStatusCodeDistribution.Values.Sum();
                foreach (var status in _analysisSummary.OverallStatusCodeDistribution.OrderBy(kvp => kvp.Key))
                {
                    var percentage = (double)status.Value / totalRequests * 100;
                    series.Points.AddXY($"{status.Key} ({percentage:F1}%)", status.Value);
                }

                chartStatusCodes.Series.Add(series);
                chartStatusCodes.Titles.Add("HTTP Status Code Distribution");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error rendering status code chart: {ex.Message}", "Chart Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PopulateTopApisGrid()
        {
            var topApis = _analysisSummary.TopApisByRequests.Take(10).ToList();

            dataGridViewTopApis?.Rows.Clear();
            dataGridViewTopApis?.Columns.Clear();

            // Add columns
            dataGridViewTopApis?.Columns.Add("Rank", "Rank");
            dataGridViewTopApis?.Columns.Add("ApiEndpoint", "API Endpoint");
            dataGridViewTopApis?.Columns.Add("Method", "Method");
            dataGridViewTopApis?.Columns.Add("Requests", "Requests");
            dataGridViewTopApis?.Columns.Add("SuccessRate", "Success Rate");

            // Set column widths
            if (dataGridViewTopApis?.Columns["Rank"] != null)
                dataGridViewTopApis.Columns["Rank"].Width = 50;
            if (dataGridViewTopApis?.Columns["ApiEndpoint"] != null)
                dataGridViewTopApis.Columns["ApiEndpoint"].Width = 200;
            if (dataGridViewTopApis?.Columns["Method"] != null)
                dataGridViewTopApis.Columns["Method"].Width = 70;
            if (dataGridViewTopApis?.Columns["Requests"] != null)
                dataGridViewTopApis.Columns["Requests"].Width = 80;
            if (dataGridViewTopApis?.Columns["SuccessRate"] != null)
                dataGridViewTopApis.Columns["SuccessRate"].Width = 100;

            // Add data
            for (int i = 0; i < topApis.Count; i++)
            {
                var api = topApis[i];
                var row = new DataGridViewRow();
                row.CreateCells(dataGridViewTopApis);

                row.Cells[0].Value = i + 1;
                row.Cells[1].Value = api.ApiEndpoint;
                row.Cells[2].Value = api.Method;
                row.Cells[3].Value = api.TotalRequests.ToString("N0");
                row.Cells[4].Value = $"{api.SuccessRate:F1}%";

                // Color code based on success rate
                if (api.SuccessRate >= 95)
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                else if (api.SuccessRate >= 90)
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
                else
                    row.DefaultCellStyle.BackColor = Color.LightCoral;

                dataGridViewTopApis?.Rows.Add(row);
            }
        }

        private void PopulateStatusCodeGrid()
        {
            dataGridViewStatusCodes?.Rows.Clear();
            dataGridViewStatusCodes?.Columns.Clear();

            // Add columns
            dataGridViewStatusCodes?.Columns.Add("StatusCode", "Status Code");
            dataGridViewStatusCodes?.Columns.Add("Count", "Count");
            dataGridViewStatusCodes?.Columns.Add("Percentage", "Percentage");
            dataGridViewStatusCodes?.Columns.Add("Description", "Description");

            // Set column widths
            if (dataGridViewStatusCodes?.Columns["StatusCode"] != null)
                dataGridViewStatusCodes.Columns["StatusCode"].Width = 100;
            if (dataGridViewStatusCodes?.Columns["Count"] != null)
                dataGridViewStatusCodes.Columns["Count"].Width = 80;
            if (dataGridViewStatusCodes?.Columns["Percentage"] != null)
                dataGridViewStatusCodes.Columns["Percentage"].Width = 100;
            if (dataGridViewStatusCodes?.Columns["Description"] != null)
                dataGridViewStatusCodes.Columns["Description"].Width = 200;

            var totalRequests = _analysisSummary.OverallStatusCodeDistribution.Values.Sum();
            var statusDescriptions = new Dictionary<int, string>
            {
                { 200, "OK" },
                { 201, "Created" },
                { 204, "No Content" },
                { 301, "Moved Permanently" },
                { 302, "Found" },
                { 400, "Bad Request" },
                { 401, "Unauthorized" },
                { 403, "Forbidden" },
                { 404, "Not Found" },
                { 500, "Internal Server Error" },
                { 502, "Bad Gateway" },
                { 503, "Service Unavailable" }
            };

            foreach (var statusCode in _analysisSummary.OverallStatusCodeDistribution.OrderBy(kvp => kvp.Key))
            {
                var row = new DataGridViewRow();
                row.CreateCells(dataGridViewStatusCodes);

                var percentage = (double)statusCode.Value / totalRequests * 100;

                row.Cells[0].Value = statusCode.Key;
                row.Cells[1].Value = statusCode.Value.ToString("N0");
                row.Cells[2].Value = $"{percentage:F1}%";
                row.Cells[3].Value = statusDescriptions.ContainsKey(statusCode.Key) ?
                    statusDescriptions[statusCode.Key] : "Unknown";

                // Color code based on status code range
                if (statusCode.Key >= 200 && statusCode.Key < 300)
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                else if (statusCode.Key >= 300 && statusCode.Key < 400)
                    row.DefaultCellStyle.BackColor = Color.LightBlue;
                else if (statusCode.Key >= 400 && statusCode.Key < 500)
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
                else if (statusCode.Key >= 500)
                    row.DefaultCellStyle.BackColor = Color.LightCoral;

                dataGridViewStatusCodes?.Rows.Add(row);
            }
        }

        private void PopulateResponseTimeGrid()
        {
            var slowestApis = _analysisSummary.SlowestApis.Take(10).ToList();

            dataGridViewResponseTimes?.Rows.Clear();
            dataGridViewResponseTimes?.Columns.Clear();

            // Add columns
            dataGridViewResponseTimes?.Columns.Add("Rank", "Rank");
            dataGridViewResponseTimes?.Columns.Add("ApiEndpoint", "API Endpoint");
            dataGridViewResponseTimes?.Columns.Add("Method", "Method");
            dataGridViewResponseTimes?.Columns.Add("AvgResponseTime", "Avg Response Time (ms)");
            dataGridViewResponseTimes?.Columns.Add("MinResponseTime", "Min (ms)");
            dataGridViewResponseTimes?.Columns.Add("MaxResponseTime", "Max (ms)");

            // Set column widths
            if (dataGridViewResponseTimes?.Columns["Rank"] != null)
                dataGridViewResponseTimes.Columns["Rank"].Width = 50;
            if (dataGridViewResponseTimes?.Columns["ApiEndpoint"] != null)
                dataGridViewResponseTimes.Columns["ApiEndpoint"].Width = 200;
            if (dataGridViewResponseTimes?.Columns["Method"] != null)
                dataGridViewResponseTimes.Columns["Method"].Width = 70;
            if (dataGridViewResponseTimes?.Columns["AvgResponseTime"] != null)
                dataGridViewResponseTimes.Columns["AvgResponseTime"].Width = 120;
            if (dataGridViewResponseTimes?.Columns["MinResponseTime"] != null)
                dataGridViewResponseTimes.Columns["MinResponseTime"].Width = 80;
            if (dataGridViewResponseTimes?.Columns["MaxResponseTime"] != null)
                dataGridViewResponseTimes.Columns["MaxResponseTime"].Width = 80;

            // Add data
            for (int i = 0; i < slowestApis.Count; i++)
            {
                var api = slowestApis[i];
                var row = new DataGridViewRow();
                row.CreateCells(dataGridViewResponseTimes);

                row.Cells[0].Value = i + 1;
                row.Cells[1].Value = api.ApiEndpoint;
                row.Cells[2].Value = api.Method;
                row.Cells[3].Value = $"{api.AverageResponseTime:F0}";
                row.Cells[4].Value = api.MinResponseTime;
                row.Cells[5].Value = api.MaxResponseTime;

                // Color code based on response time
                if (api.AverageResponseTime > 5000)
                    row.DefaultCellStyle.BackColor = Color.LightCoral;
                else if (api.AverageResponseTime > 1000)
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
                else
                    row.DefaultCellStyle.BackColor = Color.LightGreen;

                dataGridViewResponseTimes?.Rows.Add(row);
            }
        }

        private void PopulateMethodDistributionGrid()
        {
            dataGridViewMethods?.Rows.Clear();
            dataGridViewMethods?.Columns.Clear();

            // Add columns
            dataGridViewMethods?.Columns.Add("Method", "HTTP Method");
            dataGridViewMethods?.Columns.Add("Count", "Request Count");
            dataGridViewMethods?.Columns.Add("Percentage", "Percentage");

            // Set column widths
            if (dataGridViewMethods?.Columns["Method"] != null)
                dataGridViewMethods.Columns["Method"].Width = 100;
            if (dataGridViewMethods?.Columns["Count"] != null)
                dataGridViewMethods.Columns["Count"].Width = 120;
            if (dataGridViewMethods?.Columns["Percentage"] != null)
                dataGridViewMethods.Columns["Percentage"].Width = 100;

            var totalRequests = _analysisSummary.MethodDistribution.Values.Sum();
            var methodColors = new Dictionary<string, Color>
            {
                { "GET", Color.LightBlue },
                { "POST", Color.LightGreen },
                { "PUT", Color.LightYellow },
                { "DELETE", Color.LightCoral },
                { "PATCH", Color.LightPink },
                { "HEAD", Color.LightGray },
                { "OPTIONS", Color.LightCyan }
            };

            foreach (var method in _analysisSummary.MethodDistribution.OrderByDescending(kvp => kvp.Value))
            {
                var row = new DataGridViewRow();
                row.CreateCells(dataGridViewMethods);

                var percentage = (double)method.Value / totalRequests * 100;

                row.Cells[0].Value = method.Key;
                row.Cells[1].Value = method.Value.ToString("N0");
                row.Cells[2].Value = $"{percentage:F1}%";

                // Apply color coding
                row.DefaultCellStyle.BackColor = methodColors.ContainsKey(method.Key) ?
                    methodColors[method.Key] : Color.LightSteelBlue;

                dataGridViewMethods?.Rows.Add(row);
            }
        }

        private void PopulateSummaryStats()
        {
            // Create summary statistics in a rich text box
            var summary = new System.Text.StringBuilder();

            summary.AppendLine("=== IIS LOG ANALYSIS SUMMARY ===");
            summary.AppendLine();
            summary.AppendLine($"Analysis Period: {_analysisSummary.LogTimeSpan.TotalHours:F1} hours");
            summary.AppendLine($"Total Log Entries: {_analysisSummary.TotalLogEntries:N0}");
            summary.AppendLine($"Total API Endpoints: {_analysisSummary.TotalApiEndpoints:N0}");
            summary.AppendLine();

            summary.AppendLine("=== TOP PERFORMING APIS ===");
            var topByRequests = _analysisSummary.TopApisByRequests.Take(5);
            foreach (var api in topByRequests)
            {
                summary.AppendLine($"• {api.Method} {api.ApiEndpoint}: {api.TotalRequests:N0} requests ({api.SuccessRate:F1}% success)");
            }
            summary.AppendLine();

            summary.AppendLine("=== MOST ERROR-PRONE APIS ===");
            var topByErrors = _analysisSummary.TopApisByErrorRate.Take(5);
            foreach (var api in topByErrors)
            {
                summary.AppendLine($"• {api.Method} {api.ApiEndpoint}: {api.ErrorRate:F1}% error rate ({api.TotalRequests:N0} requests)");
            }
            summary.AppendLine();

            summary.AppendLine("=== SLOWEST APIS ===");
            var slowest = _analysisSummary.SlowestApis.Take(5);
            foreach (var api in slowest)
            {
                summary.AppendLine($"• {api.Method} {api.ApiEndpoint}: {api.AverageResponseTime:F0}ms avg ({api.TotalRequests:N0} requests)");
            }
            summary.AppendLine();

            summary.AppendLine("=== HTTP METHOD DISTRIBUTION ===");
            var totalMethodRequests = _analysisSummary.MethodDistribution.Values.Sum();
            foreach (var method in _analysisSummary.MethodDistribution.OrderByDescending(kvp => kvp.Value))
            {
                var percentage = (double)method.Value / totalMethodRequests * 100;
                summary.AppendLine($"• {method.Key}: {method.Value:N0} requests ({percentage:F1}%)");
            }
            summary.AppendLine();

            summary.AppendLine("=== STATUS CODE DISTRIBUTION ===");
            var totalStatusRequests = _analysisSummary.OverallStatusCodeDistribution.Values.Sum();
            foreach (var status in _analysisSummary.OverallStatusCodeDistribution.OrderBy(kvp => kvp.Key))
            {
                var percentage = (double)status.Value / totalStatusRequests * 100;
                summary.AppendLine($"• {status.Key}: {status.Value:N0} requests ({percentage:F1}%)");
            }
            summary.AppendLine();

            // Add enhanced analysis summaries
            AddPerformanceSummary(summary);
            AddSecuritySummary(summary);
            AddUserBehaviorSummary(summary);
            AddErrorAnalysisSummary(summary);
            AddApiUsageSummary(summary);

            if (richTextBoxSummary != null)
                richTextBoxSummary.Text = summary.ToString();
        }

        private void PopulatePerformanceGrid()
        {
            dataGridViewPerformance?.Rows.Clear();
            dataGridViewPerformance?.Columns.Clear();

            // Add columns
            dataGridViewPerformance?.Columns.Add("Metric", "Performance Metric");
            dataGridViewPerformance?.Columns.Add("Value", "Value");
            dataGridViewPerformance?.Columns.Add("Details", "Details");

            // Set column widths
            if (dataGridViewPerformance?.Columns["Metric"] != null)
                dataGridViewPerformance.Columns["Metric"].Width = 200;
            if (dataGridViewPerformance?.Columns["Value"] != null)
                dataGridViewPerformance.Columns["Value"].Width = 150;
            if (dataGridViewPerformance?.Columns["Details"] != null)
                dataGridViewPerformance.Columns["Details"].Width = 300;

            var performance = _analysisSummary.PerformanceAnalysis;

            // Add performance metrics
            var rows = new List<object[]>
            {
                new object[] { "Peak Traffic Hour", performance.TrafficAnalysis.PeakHour.TimeStamp.ToString("yyyy-MM-dd HH:mm"), $"{performance.TrafficAnalysis.PeakHour.RequestCount:N0} requests ({performance.TrafficAnalysis.PeakHour.PercentageOfTotal:F1}%)" },
                new object[] { "Average Requests/Hour", $"{performance.TrafficAnalysis.AverageRequestsPerHour:F1}", "Overall traffic distribution" },
                new object[] { "Response Time P50", $"{performance.ResponseTimeAnalysis.P50:F0} ms", "Median response time" },
                new object[] { "Response Time P90", $"{performance.ResponseTimeAnalysis.P90:F0} ms", "90th percentile response time" },
                new object[] { "Response Time P95", $"{performance.ResponseTimeAnalysis.P95:F0} ms", "95th percentile response time" },
                new object[] { "Response Time P99", $"{performance.ResponseTimeAnalysis.P99:F0} ms", "99th percentile response time" },
                new object[] { "Total Bandwidth Sent", FormatBytes(performance.BandwidthAnalysis.TotalBytesSent), "Total data sent to clients" },
                new object[] { "Average Bytes/Request", $"{performance.BandwidthAnalysis.AverageBytesPerRequest:F0} bytes", "Average response size" },
                new object[] { "Slowest Endpoints", $"{performance.SlowestEndpoints.Count}", "Endpoints with performance issues" }
            };

            foreach (var row in rows)
            {
                dataGridViewPerformance?.Rows.Add(row);
            }
        }

        private void PopulateSecurityGrid()
        {
            dataGridViewSecurity?.Rows.Clear();
            dataGridViewSecurity?.Columns.Clear();

            // Add columns
            dataGridViewSecurity?.Columns.Add("SecurityMetric", "Security Metric");
            dataGridViewSecurity?.Columns.Add("Value", "Value");
            dataGridViewSecurity?.Columns.Add("RiskLevel", "Risk Level");
            dataGridViewSecurity?.Columns.Add("Details", "Details");

            // Set column widths
            if (dataGridViewSecurity?.Columns["SecurityMetric"] != null)
                dataGridViewSecurity.Columns["SecurityMetric"].Width = 200;
            if (dataGridViewSecurity?.Columns["Value"] != null)
                dataGridViewSecurity.Columns["Value"].Width = 100;
            if (dataGridViewSecurity?.Columns["RiskLevel"] != null)
                dataGridViewSecurity.Columns["RiskLevel"].Width = 100;
            if (dataGridViewSecurity?.Columns["Details"] != null)
                dataGridViewSecurity.Columns["Details"].Width = 300;

            var security = _analysisSummary.SecurityAnalysis;

            // Add security metrics
            var rows = new List<object[]>
            {
                new object[] { "Authentication Failures", $"{security.AuthenticationAnalysis.TotalAuthFailures:N0}", GetRiskLevel(security.AuthenticationAnalysis.FailureRate), $"Failure rate: {security.AuthenticationAnalysis.FailureRate:F2}%" },
                new object[] { "Suspicious IPs", $"{security.SuspiciousIPs.Count:N0}", GetRiskLevel(security.SuspiciousIPs.Count, 10), $"High-risk IPs detected" },
                new object[] { "Bot Traffic", $"{security.BotAnalysis.BotTrafficPercentage:F1}%", GetRiskLevel(security.BotAnalysis.BotTrafficPercentage, 30), $"{security.BotAnalysis.TotalBotRequests:N0} bot requests" },
                new object[] { "Potential Attacks", $"{security.PotentialAttacks.Count:N0}", GetRiskLevel(security.PotentialAttacks.Count, 5), "Attack patterns detected" },
                new object[] { "Overall Security Score", $"{security.SecurityMetrics.OverallSecurityScore:F0}/100", GetSecurityScoreRisk(security.SecurityMetrics.OverallSecurityScore), "Composite security rating" },
                new object[] { "High Risk Events", $"{security.SecurityMetrics.HighRiskEvents:N0}", GetRiskLevel(security.SecurityMetrics.HighRiskEvents, 5), "Critical security events" },
                new object[] { "Medium Risk Events", $"{security.SecurityMetrics.MediumRiskEvents:N0}", "Medium", "Moderate security events" },
                new object[] { "Low Risk Events", $"{security.SecurityMetrics.LowRiskEvents:N0}", "Low", "Minor security events" }
            };

            foreach (var row in rows)
            {
                dataGridViewSecurity?.Rows.Add(row);
            }
        }

        private void PopulateUserBehaviorGrid()
        {
            dataGridViewUserBehavior?.Rows.Clear();
            dataGridViewUserBehavior?.Columns.Clear();

            // Add columns
            dataGridViewUserBehavior?.Columns.Add("BehaviorMetric", "Behavior Metric");
            dataGridViewUserBehavior?.Columns.Add("Value", "Value");
            dataGridViewUserBehavior?.Columns.Add("Percentage", "Percentage");
            dataGridViewUserBehavior?.Columns.Add("Details", "Details");

            // Set column widths
            if (dataGridViewUserBehavior?.Columns["BehaviorMetric"] != null)
                dataGridViewUserBehavior.Columns["BehaviorMetric"].Width = 200;
            if (dataGridViewUserBehavior?.Columns["Value"] != null)
                dataGridViewUserBehavior.Columns["Value"].Width = 100;
            if (dataGridViewUserBehavior?.Columns["Percentage"] != null)
                dataGridViewUserBehavior.Columns["Percentage"].Width = 100;
            if (dataGridViewUserBehavior?.Columns["Details"] != null)
                dataGridViewUserBehavior.Columns["Details"].Width = 300;

            var behavior = _analysisSummary.UserBehaviorAnalysis;

            // Add behavior metrics
            var rows = new List<object[]>
            {
                new object[] { "Total Unique Users", $"{behavior.BehaviorMetrics.TotalUniqueIPs:N0}", "100%", "Distinct IP addresses" },
                new object[] { "Estimated Sessions", $"{behavior.BehaviorMetrics.TotalEstimatedSessions:N0}", "-", $"Avg duration: {behavior.BehaviorMetrics.AverageSessionDuration:F1} min" },
                new object[] { "Bounce Rate", $"{behavior.BehaviorMetrics.BounceRate:F1}%", "-", "Single-page sessions" },
                new object[] { "Return Visitors", $"{behavior.BehaviorMetrics.ReturnVisitors:N0}", $"{behavior.BehaviorMetrics.ReturnVisitorPercentage:F1}%", "Repeat users" },
                new object[] { "Chrome Users", "-", $"{behavior.UserAgentAnalysis.BrowserAnalysis.ChromePercentage:F1}%", "Chrome browser usage" },
                new object[] { "Mobile Users", "-", $"{behavior.UserAgentAnalysis.DeviceAnalysis.MobilePercentage:F1}%", "Mobile device usage" },
                new object[] { "Desktop Users", "-", $"{behavior.UserAgentAnalysis.DeviceAnalysis.DesktopPercentage:F1}%", "Desktop usage" },
                new object[] { "Direct Traffic", "-", $"{behavior.ReferrerAnalysis.DirectTrafficPercentage:F1}%", "No referrer" },
                new object[] { "Search Engine Traffic", "-", $"{behavior.ReferrerAnalysis.SearchEnginePercentage:F1}%", "From search engines" },
                new object[] { "Social Media Traffic", "-", $"{behavior.ReferrerAnalysis.SocialMediaPercentage:F1}%", "From social platforms" }
            };

            foreach (var row in rows)
            {
                dataGridViewUserBehavior?.Rows.Add(row);
            }
        }

        private void PopulateErrorsGrid()
        {
            dataGridViewErrors?.Rows.Clear();
            dataGridViewErrors?.Columns.Clear();

            // Add columns
            dataGridViewErrors?.Columns.Add("ErrorMetric", "Error Metric");
            dataGridViewErrors?.Columns.Add("Count", "Count");
            dataGridViewErrors?.Columns.Add("Rate", "Rate/Percentage");
            dataGridViewErrors?.Columns.Add("Trend", "Trend");
            dataGridViewErrors?.Columns.Add("Details", "Details");

            // Set column widths
            if (dataGridViewErrors?.Columns["ErrorMetric"] != null)
                dataGridViewErrors.Columns["ErrorMetric"].Width = 180;
            if (dataGridViewErrors?.Columns["Count"] != null)
                dataGridViewErrors.Columns["Count"].Width = 80;
            if (dataGridViewErrors?.Columns["Rate"] != null)
                dataGridViewErrors.Columns["Rate"].Width = 100;
            if (dataGridViewErrors?.Columns["Trend"] != null)
                dataGridViewErrors.Columns["Trend"].Width = 100;
            if (dataGridViewErrors?.Columns["Details"] != null)
                dataGridViewErrors.Columns["Details"].Width = 250;

            var errors = _analysisSummary.ErrorAnalysis;

            // Add error metrics
            var rows = new List<object[]>
            {
                new object[] { "Total Errors", $"{errors.ErrorCodeAnalysis.TotalErrors:N0}", $"{errors.ErrorCodeAnalysis.OverallErrorRate:F2}%", GetTrendDirection(errors.ErrorTrends.OverallTrend), "All error responses" },
                new object[] { "Client Errors (4xx)", $"{errors.ClientVsServerErrors.ClientErrors.TotalClientErrors:N0}", $"{errors.ClientVsServerErrors.ClientErrorPercentage:F1}%", "-", "Client-side errors" },
                new object[] { "Server Errors (5xx)", $"{errors.ClientVsServerErrors.ServerErrors.TotalServerErrors:N0}", $"{errors.ClientVsServerErrors.ServerErrorPercentage:F1}%", "-", "Server-side errors" },
                new object[] { "404 Not Found", $"{errors.ClientVsServerErrors.ClientErrors.NotFoundErrors:N0}", "-", "-", "Missing resources" },
                new object[] { "401 Unauthorized", $"{errors.ClientVsServerErrors.ClientErrors.UnauthorizedErrors:N0}", "-", "-", "Authentication required" },
                new object[] { "403 Forbidden", $"{errors.ClientVsServerErrors.ClientErrors.ForbiddenErrors:N0}", "-", "-", "Access denied" },
                new object[] { "500 Internal Error", $"{errors.ClientVsServerErrors.ServerErrors.InternalServerErrors:N0}", "-", "-", "Server errors" },
                new object[] { "502 Bad Gateway", $"{errors.ClientVsServerErrors.ServerErrors.BadGatewayErrors:N0}", "-", "-", "Gateway errors" },
                new object[] { "503 Service Unavailable", $"{errors.ClientVsServerErrors.ServerErrors.ServiceUnavailableErrors:N0}", "-", "-", "Service overload" },
                new object[] { "Error Spikes Detected", $"{errors.ErrorTrends.ErrorSpikes.Count:N0}", "-", "-", "Unusual error patterns" },
                new object[] { "Problematic Endpoints", $"{errors.ProblematicEndpoints.Count:N0}", "-", "-", "High error rate endpoints" }
            };

            foreach (var row in rows)
            {
                dataGridViewErrors?.Rows.Add(row);
            }
        }

        private void PopulateApiUsageGrid()
        {
            dataGridViewApiUsage?.Rows.Clear();
            dataGridViewApiUsage?.Columns.Clear();

            // Add columns
            dataGridViewApiUsage?.Columns.Add("UsageMetric", "Usage Metric");
            dataGridViewApiUsage?.Columns.Add("Value", "Value");
            dataGridViewApiUsage?.Columns.Add("Score", "Score/Rating");
            dataGridViewApiUsage?.Columns.Add("Details", "Details");

            // Set column widths
            if (dataGridViewApiUsage?.Columns["UsageMetric"] != null)
                dataGridViewApiUsage.Columns["UsageMetric"].Width = 200;
            if (dataGridViewApiUsage?.Columns["Value"] != null)
                dataGridViewApiUsage.Columns["Value"].Width = 120;
            if (dataGridViewApiUsage?.Columns["Score"] != null)
                dataGridViewApiUsage.Columns["Score"].Width = 100;
            if (dataGridViewApiUsage?.Columns["Details"] != null)
                dataGridViewApiUsage.Columns["Details"].Width = 280;

            var usage = _analysisSummary.ApiUsageAnalytics;

            // Add usage metrics
            var rows = new List<object[]>
            {
                new object[] { "Total API Calls", $"{usage.UsageMetrics.TotalApiCalls:N0}", "-", "All requests processed" },
                new object[] { "Unique Endpoints", $"{usage.UsageMetrics.UniqueEndpoints:N0}", "-", "Distinct API endpoints" },
                new object[] { "Unique Users", $"{usage.UsageMetrics.UniqueUsers:N0}", "-", "Distinct client IPs" },
                new object[] { "Calls per User", $"{usage.UsageMetrics.AverageCallsPerUser:F1}", "-", "Average requests per IP" },
                new object[] { "Calls per Endpoint", $"{usage.UsageMetrics.AverageCallsPerEndpoint:F1}", "-", "Average usage per endpoint" },
                new object[] { "Rate Limit Violations", $"{usage.RateLimitingAnalysis.TotalViolations:N0}", GetViolationSeverity(usage.RateLimitingAnalysis.TotalViolations), "Excessive request patterns" },
                new object[] { "Peak Requests/Minute", $"{usage.RateLimitingAnalysis.Metrics.PeakRequestsPerMinute:F0}", "-", "Highest traffic burst" },
                new object[] { "API Health Score", $"{usage.UsageMetrics.HealthScore.OverallScore:F0}/100", GetHealthScoreRating(usage.UsageMetrics.HealthScore.OverallScore), "Overall API health" },
                new object[] { "Performance Score", $"{usage.UsageMetrics.HealthScore.PerformanceScore:F0}/100", "-", "Response time rating" },
                new object[] { "Reliability Score", $"{usage.UsageMetrics.HealthScore.ReliabilityScore:F0}/100", "-", "Success rate rating" },
                new object[] { "Security Score", $"{usage.UsageMetrics.HealthScore.SecurityScore:F0}/100", "-", "Security rating" },
                new object[] { "Underutilized Endpoints", $"{usage.UsageMetrics.UnderutilizedEndpoints.Count:N0}", "-", "Low-usage endpoints" },
                new object[] { "Overutilized Endpoints", $"{usage.UsageMetrics.OverutilizedEndpoints.Count:N0}", "-", "High-usage endpoints" }
            };

            foreach (var row in rows)
            {
                dataGridViewApiUsage?.Rows.Add(row);
            }
        }

        private void AddPerformanceSummary(System.Text.StringBuilder summary)
        {
            var performance = _analysisSummary.PerformanceAnalysis;

            summary.AppendLine("=== PERFORMANCE ANALYSIS ===");
            summary.AppendLine($"Peak Traffic: {performance.TrafficAnalysis.PeakHour.TimeStamp:yyyy-MM-dd HH:mm} ({performance.TrafficAnalysis.PeakHour.RequestCount:N0} requests)");
            summary.AppendLine($"Response Time P95: {performance.ResponseTimeAnalysis.P95:F0}ms");
            summary.AppendLine($"Response Time P99: {performance.ResponseTimeAnalysis.P99:F0}ms");
            summary.AppendLine($"Total Bandwidth: {FormatBytes(performance.BandwidthAnalysis.TotalBytesSent)}");
            summary.AppendLine($"Slowest Endpoints: {performance.SlowestEndpoints.Count} identified");
            summary.AppendLine();
        }

        private void AddSecuritySummary(System.Text.StringBuilder summary)
        {
            var security = _analysisSummary.SecurityAnalysis;

            summary.AppendLine("=== SECURITY ANALYSIS ===");
            summary.AppendLine($"Security Score: {security.SecurityMetrics.OverallSecurityScore:F0}/100");
            summary.AppendLine($"Authentication Failures: {security.AuthenticationAnalysis.TotalAuthFailures:N0} ({security.AuthenticationAnalysis.FailureRate:F2}%)");
            summary.AppendLine($"Suspicious IPs: {security.SuspiciousIPs.Count:N0}");
            summary.AppendLine($"Bot Traffic: {security.BotAnalysis.BotTrafficPercentage:F1}%");
            summary.AppendLine($"Potential Attacks: {security.PotentialAttacks.Count:N0} patterns detected");
            summary.AppendLine($"High Risk Events: {security.SecurityMetrics.HighRiskEvents:N0}");
            summary.AppendLine();
        }

        private void AddUserBehaviorSummary(System.Text.StringBuilder summary)
        {
            var behavior = _analysisSummary.UserBehaviorAnalysis;

            summary.AppendLine("=== USER BEHAVIOR ANALYSIS ===");
            summary.AppendLine($"Unique Users: {behavior.BehaviorMetrics.TotalUniqueIPs:N0}");
            summary.AppendLine($"Estimated Sessions: {behavior.BehaviorMetrics.TotalEstimatedSessions:N0}");
            summary.AppendLine($"Average Session Duration: {behavior.BehaviorMetrics.AverageSessionDuration:F1} minutes");
            summary.AppendLine($"Bounce Rate: {behavior.BehaviorMetrics.BounceRate:F1}%");
            summary.AppendLine($"Return Visitors: {behavior.BehaviorMetrics.ReturnVisitorPercentage:F1}%");
            summary.AppendLine($"Mobile Users: {behavior.UserAgentAnalysis.DeviceAnalysis.MobilePercentage:F1}%");
            summary.AppendLine($"Direct Traffic: {behavior.ReferrerAnalysis.DirectTrafficPercentage:F1}%");
            summary.AppendLine();
        }

        private void AddErrorAnalysisSummary(System.Text.StringBuilder summary)
        {
            var errors = _analysisSummary.ErrorAnalysis;

            summary.AppendLine("=== ERROR ANALYSIS ===");
            summary.AppendLine($"Total Errors: {errors.ErrorCodeAnalysis.TotalErrors:N0} ({errors.ErrorCodeAnalysis.OverallErrorRate:F2}%)");
            summary.AppendLine($"Client Errors (4xx): {errors.ClientVsServerErrors.ClientErrors.TotalClientErrors:N0}");
            summary.AppendLine($"Server Errors (5xx): {errors.ClientVsServerErrors.ServerErrors.TotalServerErrors:N0}");
            summary.AppendLine($"Error Trend: {GetTrendDirection(errors.ErrorTrends.OverallTrend)}");
            summary.AppendLine($"Error Spikes: {errors.ErrorTrends.ErrorSpikes.Count:N0} detected");
            summary.AppendLine($"Problematic Endpoints: {errors.ProblematicEndpoints.Count:N0}");

            // Top error codes
            var topErrors = errors.ErrorCodeAnalysis.MostCommonErrors.Take(3);
            summary.AppendLine("Top Error Codes:");
            foreach (var error in topErrors)
            {
                summary.AppendLine($"  • {error.StatusCode}: {error.Count:N0} ({error.Percentage:F1}%)");
            }
            summary.AppendLine();
        }

        private void AddApiUsageSummary(System.Text.StringBuilder summary)
        {
            var usage = _analysisSummary.ApiUsageAnalytics;

            summary.AppendLine("=== API USAGE ANALYTICS ===");
            summary.AppendLine($"API Health Score: {usage.UsageMetrics.HealthScore.OverallScore:F0}/100 ({GetHealthScoreRating(usage.UsageMetrics.HealthScore.OverallScore)})");
            summary.AppendLine($"Total API Calls: {usage.UsageMetrics.TotalApiCalls:N0}");
            summary.AppendLine($"Average Calls per User: {usage.UsageMetrics.AverageCallsPerUser:F1}");
            summary.AppendLine($"Rate Limit Violations: {usage.RateLimitingAnalysis.TotalViolations:N0}");
            summary.AppendLine($"Peak Traffic: {usage.RateLimitingAnalysis.Metrics.PeakRequestsPerMinute:F0} requests/minute");
            summary.AppendLine($"Underutilized Endpoints: {usage.UsageMetrics.UnderutilizedEndpoints.Count:N0}");
            summary.AppendLine($"Overutilized Endpoints: {usage.UsageMetrics.OverutilizedEndpoints.Count:N0}");

            // Health scores breakdown
            summary.AppendLine("Health Breakdown:");
            summary.AppendLine($"  • Performance: {usage.UsageMetrics.HealthScore.PerformanceScore:F0}/100");
            summary.AppendLine($"  • Reliability: {usage.UsageMetrics.HealthScore.ReliabilityScore:F0}/100");
            summary.AppendLine($"  • Security: {usage.UsageMetrics.HealthScore.SecurityScore:F0}/100");
            summary.AppendLine();

            // Actionable recommendations
            summary.AppendLine("=== KEY RECOMMENDATIONS ===");
            AddRecommendations(summary);
        }

        private void AddRecommendations(System.Text.StringBuilder summary)
        {
            var recommendations = new List<string>();

            // Performance recommendations
            var performance = _analysisSummary.PerformanceAnalysis;
            if (performance.ResponseTimeAnalysis.P95 > 2000)
                recommendations.Add("• Optimize response times - P95 exceeds 2 seconds");
            if (performance.SlowestEndpoints.Count > 5)
                recommendations.Add($"• Review {performance.SlowestEndpoints.Count} slow endpoints for optimization");

            // Security recommendations
            var security = _analysisSummary.SecurityAnalysis;
            if (security.SecurityMetrics.OverallSecurityScore < 75)
                recommendations.Add("• Strengthen security measures - score below 75");
            if (security.AuthenticationAnalysis.TotalAuthFailures > 100)
                recommendations.Add("• Implement rate limiting for authentication endpoints");
            if (security.SuspiciousIPs.Any(ip => ip.RiskScore >= 80))
                recommendations.Add("• Consider blocking high-risk IP addresses");

            // Error recommendations
            var errors = _analysisSummary.ErrorAnalysis;
            if (errors.ErrorCodeAnalysis.OverallErrorRate > 5)
                recommendations.Add("• Investigate high error rate - exceeds 5%");
            if (errors.ClientVsServerErrors.ServerErrors.TotalServerErrors > 0)
                recommendations.Add("• Address server errors (5xx) for better reliability");

            // Usage recommendations
            var usage = _analysisSummary.ApiUsageAnalytics;
            if (usage.UsageMetrics.HealthScore.OverallScore < 70)
                recommendations.Add("• Improve overall API health - score below 70");
            if (usage.RateLimitingAnalysis.TotalViolations > 50)
                recommendations.Add("• Implement stricter rate limiting");

            // User behavior recommendations
            var behavior = _analysisSummary.UserBehaviorAnalysis;
            if (behavior.BehaviorMetrics.BounceRate > 70)
                recommendations.Add("• Improve user engagement - high bounce rate");

            foreach (var recommendation in recommendations.Take(8))
            {
                summary.AppendLine(recommendation);
            }

            if (!recommendations.Any())
            {
                summary.AppendLine("• System appears to be performing well - no critical issues detected");
            }
        }

        // Helper methods for formatting and risk assessment
        private string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;

            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }

            return $"{number:n1} {suffixes[counter]}";
        }

        private string GetRiskLevel(double value, double threshold = 5)
        {
            if (value > threshold * 2) return "High";
            if (value > threshold) return "Medium";
            return "Low";
        }

        private string GetSecurityScoreRisk(double score)
        {
            if (score < 50) return "High";
            if (score < 75) return "Medium";
            return "Low";
        }

        private string GetTrendDirection(TrendDirection trend)
        {
            return trend switch
            {
                TrendDirection.Improving => "↓ Improving",
                TrendDirection.Worsening => "↑ Worsening",
                TrendDirection.Stable => "→ Stable",
                _ => "Unknown"
            };
        }

        private string GetViolationSeverity(int violations)
        {
            if (violations > 100) return "Critical";
            if (violations > 50) return "High";
            if (violations > 10) return "Medium";
            return "Low";
        }

        private string GetHealthScoreRating(double score)
        {
            if (score >= 90) return "Excellent";
            if (score >= 75) return "Good";
            if (score >= 60) return "Fair";
            if (score >= 40) return "Poor";
            return "Critical";
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            PopulateVisualizationData();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void btnExportData_Click(object sender, EventArgs e)
        {
            using var saveFileDialog = new SaveFileDialog
            {
                Title = "Export Visualization Data",
                Filter = "Text files (*.txt)|*.txt|All files (*.*)|*.*",
                DefaultExt = "txt",
                FileName = $"IIS_Visualization_Data_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
            };

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    System.IO.File.WriteAllText(saveFileDialog.FileName, richTextBoxSummary.Text);
                    MessageBox.Show("Data exported successfully!", "Export Complete",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error exporting data: {ex.Message}", "Export Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}
