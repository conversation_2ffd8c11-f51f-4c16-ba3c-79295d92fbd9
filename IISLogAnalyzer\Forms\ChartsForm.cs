using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using IISLogAnalyzer.Models;

namespace IISLogAnalyzer.Forms
{
    /// <summary>
    /// Form for displaying various visualizations of IIS log analysis data using simple controls
    /// </summary>
    public partial class ChartsForm : Form
    {
        private readonly OverallAnalysisSummary _analysisSummary;

        public ChartsForm(OverallAnalysisSummary analysisSummary)
        {
            _analysisSummary = analysisSummary ?? throw new ArgumentNullException(nameof(analysisSummary));
            InitializeComponent();
            PopulateVisualizationData();
        }

        private void PopulateVisualizationData()
        {
            PopulateTopApisGrid();
            PopulateStatusCodeGrid();
            PopulateStatusCodeChart();
            PopulateResponseTimeGrid();
            PopulateMethodDistributionGrid();
            PopulateSummaryStats();
        }

        private void PopulateStatusCodeChart()
        {
            try
            {
                // Defensive: Check if chartStatusCodes is initialized
                if (chartStatusCodes == null)
                    return;

                // Defensive: Check if data is available
                if (_analysisSummary?.OverallStatusCodeDistribution == null || !_analysisSummary.OverallStatusCodeDistribution.Any())
                {
                    chartStatusCodes.Series.Clear();
                    chartStatusCodes.ChartAreas.Clear();
                    chartStatusCodes.Titles.Clear();
                    chartStatusCodes.Titles.Add("No status code data available");
                    return;
                }

                // Clear previous chart data
                chartStatusCodes.Series.Clear();
                chartStatusCodes.ChartAreas.Clear();
                chartStatusCodes.Titles.Clear();

                var chartArea = new System.Windows.Forms.DataVisualization.Charting.ChartArea();
                chartStatusCodes.ChartAreas.Add(chartArea);

                var series = new System.Windows.Forms.DataVisualization.Charting.Series
                {
                    Name = "StatusCodes",
                    ChartType = System.Windows.Forms.DataVisualization.Charting.SeriesChartType.Pie,
                    IsValueShownAsLabel = true
                };

                var totalRequests = _analysisSummary.OverallStatusCodeDistribution.Values.Sum();
                foreach (var status in _analysisSummary.OverallStatusCodeDistribution.OrderBy(kvp => kvp.Key))
                {
                    var percentage = (double)status.Value / totalRequests * 100;
                    series.Points.AddXY($"{status.Key} ({percentage:F1}%)", status.Value);
                }

                chartStatusCodes.Series.Add(series);
                chartStatusCodes.Titles.Add("HTTP Status Code Distribution");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error rendering status code chart: {ex.Message}", "Chart Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PopulateTopApisGrid()
        {
            var topApis = _analysisSummary.TopApisByRequests.Take(10).ToList();

            dataGridViewTopApis.Rows.Clear();
            dataGridViewTopApis.Columns.Clear();

            // Add columns
            dataGridViewTopApis.Columns.Add("Rank", "Rank");
            dataGridViewTopApis.Columns.Add("ApiEndpoint", "API Endpoint");
            dataGridViewTopApis.Columns.Add("Method", "Method");
            dataGridViewTopApis.Columns.Add("Requests", "Requests");
            dataGridViewTopApis.Columns.Add("SuccessRate", "Success Rate");

            // Set column widths
            dataGridViewTopApis.Columns["Rank"].Width = 50;
            dataGridViewTopApis.Columns["ApiEndpoint"].Width = 200;
            dataGridViewTopApis.Columns["Method"].Width = 70;
            dataGridViewTopApis.Columns["Requests"].Width = 80;
            dataGridViewTopApis.Columns["SuccessRate"].Width = 100;

            // Add data
            for (int i = 0; i < topApis.Count; i++)
            {
                var api = topApis[i];
                var row = new DataGridViewRow();
                row.CreateCells(dataGridViewTopApis);

                row.Cells[0].Value = i + 1;
                row.Cells[1].Value = api.ApiEndpoint;
                row.Cells[2].Value = api.Method;
                row.Cells[3].Value = api.TotalRequests.ToString("N0");
                row.Cells[4].Value = $"{api.SuccessRate:F1}%";

                // Color code based on success rate
                if (api.SuccessRate >= 95)
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                else if (api.SuccessRate >= 90)
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
                else
                    row.DefaultCellStyle.BackColor = Color.LightCoral;

                dataGridViewTopApis.Rows.Add(row);
            }
        }

        private void PopulateStatusCodeGrid()
        {
            dataGridViewStatusCodes.Rows.Clear();
            dataGridViewStatusCodes.Columns.Clear();

            // Add columns
            dataGridViewStatusCodes.Columns.Add("StatusCode", "Status Code");
            dataGridViewStatusCodes.Columns.Add("Count", "Count");
            dataGridViewStatusCodes.Columns.Add("Percentage", "Percentage");
            dataGridViewStatusCodes.Columns.Add("Description", "Description");

            // Set column widths
            dataGridViewStatusCodes.Columns["StatusCode"].Width = 100;
            dataGridViewStatusCodes.Columns["Count"].Width = 80;
            dataGridViewStatusCodes.Columns["Percentage"].Width = 100;
            dataGridViewStatusCodes.Columns["Description"].Width = 200;

            var totalRequests = _analysisSummary.OverallStatusCodeDistribution.Values.Sum();
            var statusDescriptions = new Dictionary<int, string>
            {
                { 200, "OK" },
                { 201, "Created" },
                { 204, "No Content" },
                { 301, "Moved Permanently" },
                { 302, "Found" },
                { 400, "Bad Request" },
                { 401, "Unauthorized" },
                { 403, "Forbidden" },
                { 404, "Not Found" },
                { 500, "Internal Server Error" },
                { 502, "Bad Gateway" },
                { 503, "Service Unavailable" }
            };

            foreach (var statusCode in _analysisSummary.OverallStatusCodeDistribution.OrderBy(kvp => kvp.Key))
            {
                var row = new DataGridViewRow();
                row.CreateCells(dataGridViewStatusCodes);

                var percentage = (double)statusCode.Value / totalRequests * 100;

                row.Cells[0].Value = statusCode.Key;
                row.Cells[1].Value = statusCode.Value.ToString("N0");
                row.Cells[2].Value = $"{percentage:F1}%";
                row.Cells[3].Value = statusDescriptions.ContainsKey(statusCode.Key) ?
                    statusDescriptions[statusCode.Key] : "Unknown";

                // Color code based on status code range
                if (statusCode.Key >= 200 && statusCode.Key < 300)
                    row.DefaultCellStyle.BackColor = Color.LightGreen;
                else if (statusCode.Key >= 300 && statusCode.Key < 400)
                    row.DefaultCellStyle.BackColor = Color.LightBlue;
                else if (statusCode.Key >= 400 && statusCode.Key < 500)
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
                else if (statusCode.Key >= 500)
                    row.DefaultCellStyle.BackColor = Color.LightCoral;

                dataGridViewStatusCodes.Rows.Add(row);
            }
        }

        private void PopulateResponseTimeGrid()
        {
            var slowestApis = _analysisSummary.SlowestApis.Take(10).ToList();

            dataGridViewResponseTimes.Rows.Clear();
            dataGridViewResponseTimes.Columns.Clear();

            // Add columns
            dataGridViewResponseTimes.Columns.Add("Rank", "Rank");
            dataGridViewResponseTimes.Columns.Add("ApiEndpoint", "API Endpoint");
            dataGridViewResponseTimes.Columns.Add("Method", "Method");
            dataGridViewResponseTimes.Columns.Add("AvgResponseTime", "Avg Response Time (ms)");
            dataGridViewResponseTimes.Columns.Add("MinResponseTime", "Min (ms)");
            dataGridViewResponseTimes.Columns.Add("MaxResponseTime", "Max (ms)");

            // Set column widths
            dataGridViewResponseTimes.Columns["Rank"].Width = 50;
            dataGridViewResponseTimes.Columns["ApiEndpoint"].Width = 200;
            dataGridViewResponseTimes.Columns["Method"].Width = 70;
            dataGridViewResponseTimes.Columns["AvgResponseTime"].Width = 120;
            dataGridViewResponseTimes.Columns["MinResponseTime"].Width = 80;
            dataGridViewResponseTimes.Columns["MaxResponseTime"].Width = 80;

            // Add data
            for (int i = 0; i < slowestApis.Count; i++)
            {
                var api = slowestApis[i];
                var row = new DataGridViewRow();
                row.CreateCells(dataGridViewResponseTimes);

                row.Cells[0].Value = i + 1;
                row.Cells[1].Value = api.ApiEndpoint;
                row.Cells[2].Value = api.Method;
                row.Cells[3].Value = $"{api.AverageResponseTime:F0}";
                row.Cells[4].Value = api.MinResponseTime;
                row.Cells[5].Value = api.MaxResponseTime;

                // Color code based on response time
                if (api.AverageResponseTime > 5000)
                    row.DefaultCellStyle.BackColor = Color.LightCoral;
                else if (api.AverageResponseTime > 1000)
                    row.DefaultCellStyle.BackColor = Color.LightYellow;
                else
                    row.DefaultCellStyle.BackColor = Color.LightGreen;

                dataGridViewResponseTimes.Rows.Add(row);
            }
        }

        private void PopulateMethodDistributionGrid()
        {
            dataGridViewMethods.Rows.Clear();
            dataGridViewMethods.Columns.Clear();

            // Add columns
            dataGridViewMethods.Columns.Add("Method", "HTTP Method");
            dataGridViewMethods.Columns.Add("Count", "Request Count");
            dataGridViewMethods.Columns.Add("Percentage", "Percentage");

            // Set column widths
            dataGridViewMethods.Columns["Method"].Width = 100;
            dataGridViewMethods.Columns["Count"].Width = 120;
            dataGridViewMethods.Columns["Percentage"].Width = 100;

            var totalRequests = _analysisSummary.MethodDistribution.Values.Sum();
            var methodColors = new Dictionary<string, Color>
            {
                { "GET", Color.LightBlue },
                { "POST", Color.LightGreen },
                { "PUT", Color.LightYellow },
                { "DELETE", Color.LightCoral },
                { "PATCH", Color.LightPink },
                { "HEAD", Color.LightGray },
                { "OPTIONS", Color.LightCyan }
            };

            foreach (var method in _analysisSummary.MethodDistribution.OrderByDescending(kvp => kvp.Value))
            {
                var row = new DataGridViewRow();
                row.CreateCells(dataGridViewMethods);

                var percentage = (double)method.Value / totalRequests * 100;

                row.Cells[0].Value = method.Key;
                row.Cells[1].Value = method.Value.ToString("N0");
                row.Cells[2].Value = $"{percentage:F1}%";

                // Apply color coding
                row.DefaultCellStyle.BackColor = methodColors.ContainsKey(method.Key) ?
                    methodColors[method.Key] : Color.LightSteelBlue;

                dataGridViewMethods.Rows.Add(row);
            }
        }

        private void PopulateSummaryStats()
        {
            // Create summary statistics in a rich text box
            var summary = new System.Text.StringBuilder();

            summary.AppendLine("=== IIS LOG ANALYSIS SUMMARY ===");
            summary.AppendLine();
            summary.AppendLine($"Analysis Period: {_analysisSummary.LogTimeSpan.TotalHours:F1} hours");
            summary.AppendLine($"Total Log Entries: {_analysisSummary.TotalLogEntries:N0}");
            summary.AppendLine($"Total API Endpoints: {_analysisSummary.TotalApiEndpoints:N0}");
            summary.AppendLine();

            summary.AppendLine("=== TOP PERFORMING APIS ===");
            var topByRequests = _analysisSummary.TopApisByRequests.Take(5);
            foreach (var api in topByRequests)
            {
                summary.AppendLine($"• {api.Method} {api.ApiEndpoint}: {api.TotalRequests:N0} requests ({api.SuccessRate:F1}% success)");
            }
            summary.AppendLine();

            summary.AppendLine("=== MOST ERROR-PRONE APIS ===");
            var topByErrors = _analysisSummary.TopApisByErrorRate.Take(5);
            foreach (var api in topByErrors)
            {
                summary.AppendLine($"• {api.Method} {api.ApiEndpoint}: {api.ErrorRate:F1}% error rate ({api.TotalRequests:N0} requests)");
            }
            summary.AppendLine();

            summary.AppendLine("=== SLOWEST APIS ===");
            var slowest = _analysisSummary.SlowestApis.Take(5);
            foreach (var api in slowest)
            {
                summary.AppendLine($"• {api.Method} {api.ApiEndpoint}: {api.AverageResponseTime:F0}ms avg ({api.TotalRequests:N0} requests)");
            }
            summary.AppendLine();

            summary.AppendLine("=== HTTP METHOD DISTRIBUTION ===");
            var totalMethodRequests = _analysisSummary.MethodDistribution.Values.Sum();
            foreach (var method in _analysisSummary.MethodDistribution.OrderByDescending(kvp => kvp.Value))
            {
                var percentage = (double)method.Value / totalMethodRequests * 100;
                summary.AppendLine($"• {method.Key}: {method.Value:N0} requests ({percentage:F1}%)");
            }
            summary.AppendLine();

            summary.AppendLine("=== STATUS CODE DISTRIBUTION ===");
            var totalStatusRequests = _analysisSummary.OverallStatusCodeDistribution.Values.Sum();
            foreach (var status in _analysisSummary.OverallStatusCodeDistribution.OrderBy(kvp => kvp.Key))
            {
                var percentage = (double)status.Value / totalStatusRequests * 100;
                summary.AppendLine($"• {status.Key}: {status.Value:N0} requests ({percentage:F1}%)");
            }

            richTextBoxSummary.Text = summary.ToString();
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            PopulateVisualizationData();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void btnExportData_Click(object sender, EventArgs e)
        {
            using var saveFileDialog = new SaveFileDialog
            {
                Title = "Export Visualization Data",
                Filter = "Text files (*.txt)|*.txt|All files (*.*)|*.*",
                DefaultExt = "txt",
                FileName = $"IIS_Visualization_Data_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
            };

            if (saveFileDialog.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    System.IO.File.WriteAllText(saveFileDialog.FileName, richTextBoxSummary.Text);
                    MessageBox.Show("Data exported successfully!", "Export Complete",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error exporting data: {ex.Message}", "Export Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }
    }
}
